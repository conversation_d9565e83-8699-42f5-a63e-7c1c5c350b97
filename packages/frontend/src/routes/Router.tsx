import { Route, Routes } from 'react-router-dom';
import Estimates from '../components/Estimates';
import Customers from '../components/Customers';
import Layout from '../components/Layout';
import NewEstimate from '../components/Estimates/NewEstimate';
import EditEstimate from '../components/Estimates/EditEstimate';

export default function Router() {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route path="/" element={<Estimates />} />

        <Route path="/estimates" element={<Estimates />} />
        <Route path="/customers" element={<Customers />} />
      </Route>
      <Route path="/estimates/new" element={<NewEstimate />} />
      <Route path="/estimates/:id/edit" element={<EditEstimate />} />
    </Routes>
  );
}
