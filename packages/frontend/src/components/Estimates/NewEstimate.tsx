import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import EstimateTabs from './EstimateTabs';
import { useCreateEstimate } from './hooks';
import { Box, Typography } from '@mui/material';

export default function NewEstimate() {
  const navigate = useNavigate();
  const createEstimateMutation = useCreateEstimate();

  const handleSubmit = async (values: any) => {
    try {
      // Filter out line items with empty types and transform data
      const validLineItems = values.lineItems
        .filter((item: any) => item.type && item.type !== '')
        .map((item: any) => ({
          type: item.type as 'Labor' | 'Materials' | 'Equipment',
          item: item.item,
          units: item.units,
          time: item.time || undefined,
          rate: item.rate,
          margin: item.margin,
        }));

      if (validLineItems.length === 0) {
        alert('Please add at least one valid line item.');
        return;
      }

      const payload = {
        estimate: {
          name: `Estimate ${new Date().toLocaleDateString()}`,
          description: 'Generated from estimate form',
          customerId: values.customerId,
        },
        lineItems: validLineItems,
      };

      await createEstimateMutation.mutateAsync({ payload });

      navigate('/estimates');
    } catch (error) {
      console.error('Error saving estimate:', error);
    }
  };

  return (
    <Box margin="20px">
      <Helmet>
        <title>New Estimate | Project</title>
      </Helmet>

      <Box marginBottom="20px">
        <Typography variant="h4" gutterBottom>
          New Estimate
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Create a new estimate with line items and customer information
        </Typography>
      </Box>

      <EstimateTabs
        onSubmit={handleSubmit}
        isLoading={createEstimateMutation.isLoading}
      />
    </Box>
  );
}
