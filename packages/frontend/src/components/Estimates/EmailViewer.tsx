import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Toolbar,
  IconButton,
  Tooltip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Chip,
} from '@mui/material';
import {
  Send as SendIcon,
  Refresh as RefreshIcon,
  Email as EmailIcon,
} from '@mui/icons-material';
import useProjectClient from '../../hooks/useProjectClient';
import { useSnackbar } from '../../context/snackbar';
import { useEstimate } from './hooks';
import Snackbar from '../Snackbar';

interface EmailViewerProps {
  estimateId: number;
}

interface EmailPreviewData {
  subject: string;
  recipientEmail: string;
  recipientName: string;
  htmlContent: string;
  textContent: string;
}

interface EmailPreviewResponse {
  success: boolean;
  estimateId: number;
  preview: EmailPreviewData;
}

export default function EmailViewer({ estimateId }: EmailViewerProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [emailPreview, setEmailPreview] = useState<EmailPreviewData | null>(
    null,
  );
  const [error, setError] = useState<string | null>(null);
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [customSubject, setCustomSubject] = useState('');
  const [customRecipient, setCustomRecipient] = useState('');

  const client = useProjectClient();
  const snackbar = useSnackbar();
  const { data: estimate } = useEstimate(estimateId);

  const generateEmailPreview = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response: EmailPreviewResponse =
        await client.generateEstimateEmailPreview(estimateId);

      if (response.success) {
        setEmailPreview(response.preview);
        setCustomSubject(response.preview.subject);
        setCustomRecipient(response.preview.recipientEmail);
      } else {
        setError('Failed to generate email preview');
      }
    } catch (error) {
      console.error('Error generating email preview:', error);
      setError('Failed to generate email preview');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSendEmail = async () => {
    if (!emailPreview) return;

    setIsSending(true);
    try {
      const response = await client.sendEstimateEmail(estimateId, {
        recipientEmail: customRecipient || emailPreview.recipientEmail,
        recipientName: emailPreview.recipientName,
        subject: customSubject || emailPreview.subject,
      });

      if (response.success) {
        snackbar.appoint(<Snackbar label={response.message} type="success" />);
        setSendDialogOpen(false);
      } else {
        snackbar.appoint(
          <Snackbar label="Failed to send email" type="error" />,
        );
      }
    } catch (error) {
      console.error('Error sending email:', error);
      snackbar.appoint(<Snackbar label="Failed to send email" type="error" />);
    } finally {
      setIsSending(false);
    }
  };

  // Auto-generate email preview when component mounts
  useEffect(() => {
    generateEmailPreview();
  }, [estimateId]); // eslint-disable-line react-hooks/exhaustive-deps

  if (isGenerating && !emailPreview) {
    return (
      <Paper sx={{ padding: 4, textAlign: 'center', minHeight: 400 }}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
        >
          <CircularProgress size={48} sx={{ marginBottom: 2 }} />
          <Typography variant="h6" gutterBottom>
            Generating Email Preview...
          </Typography>
          <Typography variant="body2" color="textSecondary">
            This may take a few moments
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper sx={{ padding: 4, textAlign: 'center', minHeight: 400 }}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
        >
          <Alert severity="error" sx={{ marginBottom: 2 }}>
            {error}
          </Alert>
          <Button
            variant="contained"
            onClick={generateEmailPreview}
            disabled={isGenerating}
            startIcon={<RefreshIcon />}
          >
            Retry
          </Button>
        </Box>
      </Paper>
    );
  }

  if (!emailPreview) {
    return (
      <Paper sx={{ padding: 4, textAlign: 'center', minHeight: 400 }}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
        >
          <EmailIcon
            sx={{ fontSize: 64, color: 'text.secondary', marginBottom: 2 }}
          />
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No Email Preview Available
          </Typography>
          <Button
            variant="contained"
            onClick={generateEmailPreview}
            disabled={isGenerating}
            startIcon={<RefreshIcon />}
          >
            Generate Email Preview
          </Button>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Email Toolbar */}
      <Toolbar
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          backgroundColor: '#f5f5f5',
        }}
      >
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          Email Preview - Estimate #{estimateId}
        </Typography>

        <Tooltip title="Send Email">
          <IconButton
            onClick={() => setSendDialogOpen(true)}
            color="primary"
            disabled={!estimate?.customer?.email}
          >
            <SendIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Regenerate Preview">
          <IconButton onClick={generateEmailPreview} disabled={isGenerating}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Toolbar>

      {/* Email Preview Content */}
      <Box sx={{ flexGrow: 1, padding: 2 }}>
        {/* Email Headers */}
        <Box sx={{ marginBottom: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: 1 }}>
            <Typography
              variant="body2"
              sx={{ fontWeight: 'bold', minWidth: 60 }}
            >
              To:
            </Typography>
            <Chip
              label={emailPreview.recipientEmail}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: 1 }}>
            <Typography
              variant="body2"
              sx={{ fontWeight: 'bold', minWidth: 60 }}
            >
              Subject:
            </Typography>
            <Typography variant="body2">{emailPreview.subject}</Typography>
          </Box>
        </Box>

        <Divider sx={{ marginBottom: 2 }} />

        {/* Email Content */}
        <Box
          sx={{
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            overflow: 'hidden',
            minHeight: 500,
          }}
        >
          <iframe
            srcDoc={emailPreview.htmlContent}
            style={{
              width: '100%',
              height: '500px',
              border: 'none',
            }}
            title={`Email Preview - Estimate ${estimateId}`}
          />
        </Box>
      </Box>

      {/* Send Email Dialog */}
      <Dialog
        open={sendDialogOpen}
        onClose={() => setSendDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Send Estimate Email</DialogTitle>
        <DialogContent>
          <Box sx={{ paddingTop: 1 }}>
            <TextField
              fullWidth
              label="Recipient Email"
              value={customRecipient}
              onChange={(e) => setCustomRecipient(e.target.value)}
              margin="normal"
              type="email"
              helperText="Email address where the estimate will be sent"
            />
            <TextField
              fullWidth
              label="Subject"
              value={customSubject}
              onChange={(e) => setCustomSubject(e.target.value)}
              margin="normal"
              helperText="Email subject line"
            />
            <Alert severity="info" sx={{ marginTop: 2 }}>
              This is a mock implementation. The email will not actually be
              sent.
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSendDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSendEmail}
            variant="contained"
            disabled={isSending || !customRecipient}
            startIcon={
              isSending ? <CircularProgress size={16} /> : <SendIcon />
            }
          >
            {isSending ? 'Sending...' : 'Send Email'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
}
