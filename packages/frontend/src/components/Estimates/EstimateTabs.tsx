import { useState } from 'react';
import { Box, Tabs, Tab, Typography, Paper } from '@mui/material';
import {
  Email as EmailIcon,
  PictureAsPdf as PdfIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import EstimateForm from './EstimateForm';
import PdfViewer from './PdfViewer';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`estimate-tabpanel-${index}`}
      aria-labelledby={`estimate-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    'id': `estimate-tab-${index}`,
    'aria-controls': `estimate-tabpanel-${index}`,
  };
}

interface EstimateTabsProps {
  onSubmit: (values: any) => Promise<void>;
  initialValues?: any;
  isLoading?: boolean;
  estimateId?: number;
}

// Placeholder components for Email and PDF views
function EmailView({ estimateId }: { estimateId?: number }) {
  return (
    <Paper sx={{ padding: 3, minHeight: 400 }}>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100%"
      >
        <EmailIcon
          sx={{ fontSize: 64, color: 'text.secondary', marginBottom: 2 }}
        />
        <Typography variant="h6" color="textSecondary" gutterBottom>
          Email Preview
        </Typography>
        <Typography variant="body2" color="textSecondary" textAlign="center">
          Email preview functionality will be implemented here.
          {estimateId && ` (Estimate ID: ${estimateId})`}
        </Typography>
        <Box marginTop={2}>
          <Typography variant="body2" color="textSecondary">
            Features to include:
          </Typography>
          <ul style={{ color: '#666', fontSize: '14px', marginTop: '8px' }}>
            <li>Email template preview</li>
            <li>Recipient selection</li>
            <li>Subject line customization</li>
            <li>Attachment options</li>
            <li>Send functionality</li>
          </ul>
        </Box>
      </Box>
    </Paper>
  );
}

function PdfView({ estimateId }: { estimateId?: number }) {
  if (!estimateId) {
    return (
      <Paper sx={{ padding: 3, minHeight: 400 }}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
        >
          <PdfIcon
            sx={{ fontSize: 64, color: 'text.secondary', marginBottom: 2 }}
          />
          <Typography variant="h6" color="textSecondary" gutterBottom>
            PDF Preview
          </Typography>
          <Typography variant="body2" color="textSecondary" textAlign="center">
            Save the estimate to generate a PDF preview.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return <PdfViewer estimateId={estimateId} />;
}

export default function EstimateTabs({
  onSubmit,
  initialValues,
  isLoading = false,
  estimateId,
}: EstimateTabsProps) {
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange} aria-label="estimate tabs">
          <Tab
            icon={<EditIcon />}
            label="Edit"
            {...a11yProps(0)}
            sx={{ minHeight: 48 }}
          />
          <Tab
            icon={<EmailIcon />}
            label="Email View"
            {...a11yProps(1)}
            sx={{ minHeight: 48 }}
          />
          <Tab
            icon={<PdfIcon />}
            label="PDF View"
            {...a11yProps(2)}
            sx={{ minHeight: 48 }}
          />
        </Tabs>
      </Box>

      <TabPanel value={value} index={0}>
        <Box marginTop={2}>
          <EstimateForm
            onSubmit={onSubmit}
            initialValues={initialValues}
            isLoading={isLoading}
          />
        </Box>
      </TabPanel>

      <TabPanel value={value} index={1}>
        <Box marginTop={2}>
          <EmailView estimateId={estimateId} />
        </Box>
      </TabPanel>

      <TabPanel value={value} index={2}>
        <Box marginTop={2}>
          <PdfView estimateId={estimateId} />
        </Box>
      </TabPanel>
    </Box>
  );
}
