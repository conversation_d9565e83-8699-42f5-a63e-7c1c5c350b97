import {
  FormControl,
  Select,
  MenuItem,
  Box,
  Typography,
  CircularProgress,
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { useCustomers, Customer } from '../Customers/hooks';
import { useModal } from '../../context/modal';
import CustomerDrawer from '../Customers/CustomerDrawer';

interface Props {
  value?: number | null;
  onChange: (customerId: number | null) => void;
  error?: boolean;
  disabled?: boolean;
}

export default function CustomerDropdown({
  value,
  onChange,
  error = false,
  disabled = false,
}: Props) {
  const modal = useModal();
  const { data: customers = [], isLoading, isError } = useCustomers();

  const handleAddNewCustomer = async () => {
    try {
      const newCustomer = (await modal.appoint(<CustomerDrawer />)) as Customer;
      if (newCustomer) {
        onChange(newCustomer.id);
      }
    } catch (error) {
      // User cancelled or error occurred
      console.log('Customer creation cancelled or failed');
    }
  };

  const getCustomerDisplayName = (customer: Customer): string => {
    if (customer.customerDisplayName) {
      return customer.customerDisplayName;
    }
    if (customer.companyName) {
      return customer.companyName;
    }
    const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
    return nameParts.length > 0
      ? nameParts.join(' ')
      : `Customer ${customer.id}`;
  };

  const selectedCustomer = customers.find((c) => c.id === value);

  if (isError) {
    return (
      <Box>
        <Typography color="error" variant="body2">
          Error loading customers
        </Typography>
      </Box>
    );
  }

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <FormControl fullWidth error={error} disabled={disabled} size="small">
        <Select
          value={value || ''}
          onChange={(e) => {
            const selectedValue = e.target.value;
            if (selectedValue === 'add_new') {
              handleAddNewCustomer();
              return;
            }
            onChange(selectedValue ? Number(selectedValue) : null);
          }}
          displayEmpty
          renderValue={(selected) => {
            if (!selected) {
              return (
                <Typography color="textSecondary">Select a customer</Typography>
              );
            }
            return selectedCustomer
              ? getCustomerDisplayName(selectedCustomer)
              : 'Unknown Customer';
          }}
        >
          {/* Add New Customer Option */}
          <MenuItem value="add_new" divider>
            <Box display="flex" alignItems="center" gap={1}>
              <AddIcon fontSize="small" />
              <Typography>Add New Customer</Typography>
            </Box>
          </MenuItem>

          {/* Existing Customers */}
          {customers.length > 0 && [
            ...customers.map((customer) => (
              <MenuItem key={customer.id} value={customer.id}>
                <Box>
                  <Typography variant="body2">
                    {getCustomerDisplayName(customer)}
                  </Typography>
                  {customer.email && (
                    <Typography variant="caption" color="textSecondary">
                      {customer.email}
                    </Typography>
                  )}
                </Box>
              </MenuItem>
            )),
          ]}

          {/* Loading State */}
          {isLoading && (
            <MenuItem disabled>
              <Box display="flex" alignItems="center" gap={1}>
                <CircularProgress size={16} />
                <Typography>Loading customers...</Typography>
              </Box>
            </MenuItem>
          )}

          {/* Empty State */}
          {!isLoading && customers.length === 0 && (
            <MenuItem disabled>
              <Typography color="textSecondary">No customers found</Typography>
            </MenuItem>
          )}
        </Select>
      </FormControl>
    </Box>
  );
}
