import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Toolbar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import useProjectClient from '../../hooks/useProjectClient';
import { useSnackbar } from '../../context/snackbar';
import Snackbar from '../Snackbar';

interface PdfViewerProps {
  estimateId: number;
}

interface PdfGenerationResponse {
  success: boolean;
  filename: string;
  downloadUrl: string;
  viewUrl: string;
}

export default function PdfViewer({ estimateId }: PdfViewerProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [pdfData, setPdfData] = useState<PdfGenerationResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const client = useProjectClient();
  const snackbar = useSnackbar();

  const generatePdf = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await client.generateEstimatePdf(estimateId);
      setPdfData(response);
      snackbar.appoint(
        <Snackbar label="PDF generated successfully" type="success" />,
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      setError('Failed to generate PDF. Please try again.');
      snackbar.appoint(
        <Snackbar label="Failed to generate PDF" type="error" />,
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    if (pdfData) {
      const downloadUrl = client.getPdfDownloadUrl(pdfData.filename);
      // Create a temporary link to trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = pdfData.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      snackbar.appoint(
        <Snackbar label="PDF download started" type="success" />,
      );
    }
  };

  const handlePrint = () => {
    if (pdfData) {
      const printUrl = client.getPdfPrintUrl(pdfData.filename);
      // Open PDF in new tab for printing
      window.open(printUrl, '_blank');
    }
  };

  const handleRefresh = () => {
    generatePdf();
  };

  // Auto-generate PDF when component mounts
  useEffect(() => {
    generatePdf();
  }, [estimateId]); // eslint-disable-line react-hooks/exhaustive-deps

  if (isGenerating && !pdfData) {
    return (
      <Paper sx={{ padding: 4, textAlign: 'center', minHeight: 400 }}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
        >
          <CircularProgress size={48} sx={{ marginBottom: 2 }} />
          <Typography variant="h6" gutterBottom>
            Generating PDF...
          </Typography>
          <Typography variant="body2" color="textSecondary">
            This may take a few moments
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (error && !pdfData) {
    return (
      <Paper sx={{ padding: 4, minHeight: 400 }}>
        <Alert severity="error" sx={{ marginBottom: 2 }}>
          {error}
        </Alert>
        <Box textAlign="center">
          <Button
            variant="contained"
            onClick={generatePdf}
            disabled={isGenerating}
            startIcon={<RefreshIcon />}
          >
            Try Again
          </Button>
        </Box>
      </Paper>
    );
  }

  if (!pdfData) {
    return (
      <Paper sx={{ padding: 4, textAlign: 'center', minHeight: 400 }}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
        >
          <PdfIcon
            sx={{ fontSize: 64, color: 'text.secondary', marginBottom: 2 }}
          />
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No PDF Available
          </Typography>
          <Button
            variant="contained"
            onClick={generatePdf}
            disabled={isGenerating}
            startIcon={<RefreshIcon />}
          >
            Generate PDF
          </Button>
        </Box>
      </Paper>
    );
  }

  const viewUrl = client.getPdfViewUrl(pdfData.filename);

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* PDF Toolbar */}
      <Toolbar
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          backgroundColor: '#f5f5f5',
        }}
      >
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          Estimate #{estimateId} PDF
        </Typography>

        <Tooltip title="Download PDF">
          <IconButton onClick={handleDownload} color="primary">
            <DownloadIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Print PDF">
          <IconButton onClick={handlePrint} color="primary">
            <PrintIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Regenerate PDF">
          <IconButton onClick={handleRefresh} disabled={isGenerating}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Toolbar>

      {/* PDF Viewer */}
      <Box sx={{ flexGrow: 1, position: 'relative', minHeight: 600 }}>
        {isLoading && (
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            bottom={0}
            display="flex"
            alignItems="center"
            justifyContent="center"
            bgcolor="rgba(255, 255, 255, 0.8)"
            zIndex={1}
          >
            <CircularProgress />
          </Box>
        )}

        <iframe
          src={viewUrl}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            minHeight: '600px',
          }}
          title={`Estimate ${estimateId} PDF`}
          onLoad={() => setIsLoading(false)}
          onLoadStart={() => setIsLoading(true)}
        />
      </Box>

      {/* Action Buttons */}
      <Box
        sx={{
          padding: 2,
          borderTop: 1,
          borderColor: 'divider',
          backgroundColor: '#f9f9f9',
        }}
      >
        <Box display="flex" gap={2} justifyContent="center">
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={handleDownload}
            color="primary"
          >
            Download
          </Button>

          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={handlePrint}
            color="primary"
          >
            Print
          </Button>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={isGenerating}
          >
            Regenerate
          </Button>
        </Box>
      </Box>
    </Paper>
  );
}
