import React from 'react';
import { FormControl, Select, MenuItem } from '@mui/material';
import { useItemsForLineItemType } from '../Lookups/hooks';

interface Props {
  lineItemType: string;
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  disabled?: boolean;
}

export default function LineItemDropdown({
  lineItemType,
  value,
  onChange,
  error = false,
  disabled = false,
}: Props) {
  const { data: items = [], isLoading } = useItemsForLineItemType(lineItemType);

  return (
    <FormControl size="small" fullWidth error={error}>
      <Select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        displayEmpty
        disabled={disabled || !lineItemType || isLoading}
      >
        <MenuItem value="">
          <em>Select Item</em>
        </MenuItem>
        {items.map((item) => (
          <MenuItem key={item.id} value={item.value}>
            {item.name}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
