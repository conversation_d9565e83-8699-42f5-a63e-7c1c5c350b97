import { useMutation, useQuery, useQueryClient } from 'react-query';
import useProjectClient from '../../hooks/useProjectClient';
import { Customer } from '../Customers/hooks';

export function useCustomersTable() {
  const client = useProjectClient();

  return useQuery<Customer[], Error>('get-customers', () => client.getCustomers(), {
    onError: (error) => {
      console.error('Error fetching customers:', error);
    },
  });
}

export function useDeleteCustomer() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (id: number) => {
      return client.deleteCustomer(id);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-customers');
      },
      onError: (error) => {
        console.error('Error deleting customer:', error);
      },
    },
  );
}
