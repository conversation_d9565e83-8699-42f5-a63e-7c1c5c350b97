import { 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableRow, 
  Paper,
  Box,
  Typography 
} from '@mui/material';

import { useCustomersTable, useDeleteCustomer } from './hooks';
import Loader from '../Loader';
import ErrorDisplay from '../ErrorDisplay';
import TableHeaderRow from '../TableHeaderRow';
import { columns } from './columns';
import { Customer } from '../Customers/hooks';
import { useModal } from '../../context/modal';
import { useSnackbar } from '../../context/snackbar';
import CustomerDrawer from '../Customers/CustomerDrawer';
import Snackbar from '../Snackbar';

interface ConfirmDeleteModalProps {
  customer: Customer;
  onConfirm: () => void;
  onCancel: () => void;
}

function ConfirmDeleteModal({ customer, onConfirm, onCancel }: ConfirmDeleteModalProps) {
  const getCustomerDisplayName = (): string => {
    if (customer.customerDisplayName) {
      return customer.customerDisplayName;
    }
    if (customer.companyName) {
      return customer.companyName;
    }
    const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
    return nameParts.length > 0 ? nameParts.join(' ') : `Customer ${customer.id}`;
  };

  return (
    <Box padding="20px" maxWidth="400px">
      <Typography variant="h6" gutterBottom>
        Delete Customer
      </Typography>
      <Typography variant="body1" marginBottom="20px">
        Are you sure you want to delete "{getCustomerDisplayName()}"? 
        This action cannot be undone.
      </Typography>
      <Box display="flex" gap="10px" justifyContent="flex-end">
        <button 
          onClick={onCancel}
          style={{
            padding: '8px 16px',
            border: '1px solid #ccc',
            background: 'white',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
        <button 
          onClick={onConfirm}
          style={{
            padding: '8px 16px',
            border: 'none',
            background: '#f44336',
            color: 'white',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Delete
        </button>
      </Box>
    </Box>
  );
}

export default function CustomerTable() {
  const modal = useModal();
  const snackbar = useSnackbar();
  const { data: customers = [], isLoading, isError } = useCustomersTable();
  const deleteCustomerMutation = useDeleteCustomer();

  const handleEdit = async (customer: Customer) => {
    try {
      await modal.appoint(<CustomerDrawer customer={customer} />);
    } catch (error) {
      // User cancelled
    }
  };

  const handleDelete = async (customer: Customer) => {
    try {
      await modal.appoint(
        <ConfirmDeleteModal
          customer={customer}
          onConfirm={async () => {
            try {
              await deleteCustomerMutation.mutateAsync(customer.id);
              snackbar.appoint(
                <Snackbar label="Customer deleted successfully" type="success" />
              );
              modal.dismiss();
            } catch (error) {
              console.error('Error deleting customer:', error);
              snackbar.appoint(
                <Snackbar label="Failed to delete customer" type="error" />
              );
            }
          }}
          onCancel={() => modal.dismiss()}
        />
      );
    } catch (error) {
      // User cancelled
    }
  };

  if (isLoading) return <Loader />;

  if (isError) return <ErrorDisplay />;

  if (customers.length === 0) {
    return (
      <Paper sx={{ padding: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="textSecondary">
          No customers found
        </Typography>
        <Typography variant="body2" color="textSecondary" marginTop={1}>
          Create your first customer to get started.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper>
      <TableContainer>
        <Table stickyHeader aria-label="Customers Table">
          <TableHeaderRow columns={columns} />
          <TableBody>
            {customers.map((customer) => (
              <TableRow hover key={customer.id}>
                {columns.map((column, index) => (
                  <TableCell
                    key={index}
                    style={{
                      minWidth: column?.minWidth,
                      maxWidth: column?.maxWidth,
                      width: column?.width,
                    }}
                  >
                    {column.format 
                      ? column.format(customer, {
                          onEdit: () => handleEdit(customer),
                          onDelete: () => handleDelete(customer),
                        })
                      : String(customer[column.id as keyof Customer] || '')
                    }
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
}
