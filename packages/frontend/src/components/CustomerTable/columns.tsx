import { Box, IconButton } from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { Customer } from '../Customers/hooks';
import { formatDate } from '../../utilities/format';
import { Column } from '../../types/table';

enum ColumnType {
  Id = 'id',
  CustomerDisplayName = 'customerDisplayName',
  CompanyName = 'companyName',
  Email = 'email',
  PhoneNumber = 'phoneNumber',
  BillingCity = 'billingCity',
  CreatedAt = 'createdAt',
  Actions = 'actions',
}

interface ActionOptions {
  onEdit?: (customer: Customer) => void;
  onDelete?: (customer: Customer) => void;
}

const getCustomerDisplayName = (customer: Customer): string => {
  if (customer.customerDisplayName) {
    return customer.customerDisplayName;
  }
  if (customer.companyName) {
    return customer.companyName;
  }
  const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
  return nameParts.length > 0 ? nameParts.join(' ') : `Customer ${customer.id}`;
};

export const columns: Column<ColumnType, Customer>[] = [
  {
    id: ColumnType.Id,
    label: 'ID',
    minWidth: 60,
    maxWidth: 80,
  },
  {
    id: ColumnType.CustomerDisplayName,
    label: 'Customer Name',
    minWidth: 200,
    format: (customer) => {
      return getCustomerDisplayName(customer);
    },
  },
  {
    id: ColumnType.CompanyName,
    label: 'Company',
    minWidth: 180,
    format: (customer) => {
      return customer.companyName || '—';
    },
  },
  {
    id: ColumnType.Email,
    label: 'Email',
    minWidth: 200,
    format: (customer) => {
      return customer.email || '—';
    },
  },
  {
    id: ColumnType.PhoneNumber,
    label: 'Phone',
    minWidth: 140,
    format: (customer) => {
      return customer.phoneNumber || '—';
    },
  },
  {
    id: ColumnType.BillingCity,
    label: 'City',
    minWidth: 120,
    format: (customer) => {
      return customer.billingCity || '—';
    },
  },
  {
    id: ColumnType.CreatedAt,
    label: 'Created',
    minWidth: 120,
    format: (customer) => {
      return formatDate(customer.createdAt);
    },
  },
  {
    id: ColumnType.Actions,
    label: 'Actions',
    minWidth: 120,
    format: (customer, options?: ActionOptions) => {
      return (
        <Box display="flex" gap={1}>
          <IconButton
            size="small"
            onClick={() => options?.onEdit?.(customer)}
            title="Edit Customer"
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => options?.onDelete?.(customer)}
            title="Delete Customer"
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      );
    },
  },
];
