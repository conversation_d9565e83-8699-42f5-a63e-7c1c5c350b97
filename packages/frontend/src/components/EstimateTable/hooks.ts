import { useMutation, useQuery, useQueryClient } from 'react-query';
import useProjectClient from '../../hooks/useProjectClient';
import { Estimate } from '../../interfaces';

export function useEstimatesTable() {
  const client = useProjectClient();

  return useQuery<Estimate[], Error>('get-estimates', () => client.getEstimates(), {
    onError: (error) => {
      console.error('Error fetching estimates:', error);
    },
  });
}

export function useDeleteEstimate() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (id: number) => {
      return client.deleteEstimate(id);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-estimates');
      },
      onError: (error) => {
        console.error('Error deleting estimate:', error);
      },
    },
  );
}
