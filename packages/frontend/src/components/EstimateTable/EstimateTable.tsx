import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Box,
  Typography,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { useEstimatesTable, useDeleteEstimate } from './hooks';
import Loader from '../Loader';
import ErrorDisplay from '../ErrorDisplay';
import TableHeaderRow from '../TableHeaderRow';
import { columns } from './columns';
import { Estimate } from '../../interfaces';
import { useModal } from '../../context/modal';
import { useSnackbar } from '../../context/snackbar';
import Snackbar from '../Snackbar';

interface ConfirmDeleteModalProps {
  estimate: Estimate;
  onConfirm: () => void;
  onCancel: () => void;
}

function ConfirmDeleteModal({
  estimate,
  onConfirm,
  onCancel,
}: ConfirmDeleteModalProps) {
  return (
    <Box padding="20px" maxWidth="400px">
      <Typography variant="h6" gutterBottom>
        Delete Estimate
      </Typography>
      <Typography variant="body1" marginBottom="20px">
        Are you sure you want to delete "
        {estimate.name || `Estimate ${estimate.id}`}"? This action cannot be
        undone.
      </Typography>
      <Box display="flex" gap="10px" justifyContent="flex-end">
        <button
          onClick={onCancel}
          style={{
            padding: '8px 16px',
            border: '1px solid #ccc',
            background: 'white',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Cancel
        </button>
        <button
          onClick={onConfirm}
          style={{
            padding: '8px 16px',
            border: 'none',
            background: '#f44336',
            color: 'white',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Delete
        </button>
      </Box>
    </Box>
  );
}

export default function EstimateTable() {
  const navigate = useNavigate();
  const modal = useModal();
  const snackbar = useSnackbar();
  const { data: estimates = [], isLoading, isError } = useEstimatesTable();
  const deleteEstimateMutation = useDeleteEstimate();

  const handleEdit = (estimate: Estimate) => {
    // Navigate to edit page (we'll implement this later)
    navigate(`/estimates/${estimate.id}/edit`);
  };

  const handleDelete = async (estimate: Estimate) => {
    try {
      await modal.appoint(
        <ConfirmDeleteModal
          estimate={estimate}
          onConfirm={async () => {
            try {
              await deleteEstimateMutation.mutateAsync(estimate.id);
              snackbar.appoint(
                <Snackbar
                  label="Estimate deleted successfully"
                  type="success"
                />,
              );
              modal.dismiss();
            } catch (error) {
              console.error('Error deleting estimate:', error);
              snackbar.appoint(
                <Snackbar label="Failed to delete estimate" type="error" />,
              );
            }
          }}
          onCancel={() => modal.dismiss()}
        />,
      );
    } catch (error) {
      // User cancelled
    }
  };

  if (isLoading) return <Loader />;

  if (isError) return <ErrorDisplay />;

  if (estimates.length === 0) {
    return (
      <Paper sx={{ padding: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="textSecondary">
          No estimates found
        </Typography>
        <Typography variant="body2" color="textSecondary" marginTop={1}>
          Create your first estimate to get started.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper>
      <TableContainer>
        <Table stickyHeader aria-label="Estimates Table">
          <TableHeaderRow columns={columns} />
          <TableBody>
            {estimates.map((estimate) => (
              <TableRow hover key={estimate.id}>
                {columns.map((column, index) => (
                  <TableCell
                    key={index}
                    style={{
                      minWidth: column?.minWidth,
                      maxWidth: column?.maxWidth,
                      width: column?.width,
                    }}
                  >
                    {column.format
                      ? column.format(estimate, {
                          onEdit: () => handleEdit(estimate),
                          onDelete: () => handleDelete(estimate),
                        })
                      : String(estimate[column.id as keyof Estimate] || '')}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
}
