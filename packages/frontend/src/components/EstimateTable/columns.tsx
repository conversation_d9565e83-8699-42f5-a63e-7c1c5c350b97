import { Box, <PERSON>con<PERSON>utton, Chip } from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { Estimate } from '../../interfaces';
import { formatDate, formatCurrency } from '../../utilities/format';
import { Column } from '../../types/table';

enum ColumnType {
  Id = 'id',
  Name = 'name',
  Customer = 'customer',
  TotalCost = 'totalCost',
  TotalPrice = 'totalPrice',
  CreatedAt = 'createdAt',
  Actions = 'actions',
}

interface ActionOptions {
  onEdit?: (estimate: Estimate) => void;
  onDelete?: (estimate: Estimate) => void;
}

const getCustomerDisplayName = (estimate: Estimate): string => {
  if (!estimate.customer) return 'No Customer';

  const { customer } = estimate;
  if (customer.customerDisplayName) {
    return customer.customerDisplayName;
  }
  if (customer.companyName) {
    return customer.companyName;
  }
  const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
  return nameParts.length > 0 ? nameParts.join(' ') : `Customer ${customer.id}`;
};

export const columns: Column<ColumnType, Estimate>[] = [
  {
    id: ColumnType.Id,
    label: 'ID',
    minWidth: 60,
    maxWidth: 80,
  },
  {
    id: ColumnType.Name,
    label: 'Name',
    minWidth: 200,
    format: (estimate) => {
      return estimate.name || `Estimate ${estimate.id}`;
    },
  },
  {
    id: ColumnType.Customer,
    label: 'Customer',
    minWidth: 180,
    format: (estimate) => {
      const customerName = getCustomerDisplayName(estimate);
      return (
        <Box>
          <Box>{customerName}</Box>
          {estimate.customer?.email && (
            <Box
              component="span"
              sx={{
                fontSize: '0.75rem',
                color: 'text.secondary',
                display: 'block',
              }}
            >
              {estimate.customer.email}
            </Box>
          )}
        </Box>
      );
    },
  },
  {
    id: ColumnType.TotalCost,
    label: 'Total Cost',
    minWidth: 120,
    format: (estimate) => {
      return (
        <Chip
          label={formatCurrency(estimate.totalCost)}
          size="small"
          variant="outlined"
          color="default"
        />
      );
    },
  },
  {
    id: ColumnType.TotalPrice,
    label: 'Total Price',
    minWidth: 120,
    format: (estimate) => {
      return (
        <Chip
          label={formatCurrency(estimate.totalPrice)}
          size="small"
          variant="filled"
          color="primary"
        />
      );
    },
  },
  {
    id: ColumnType.CreatedAt,
    label: 'Created',
    minWidth: 120,
    format: (estimate) => {
      return formatDate(estimate.createdAt);
    },
  },
  {
    id: ColumnType.Actions,
    label: 'Actions',
    minWidth: 120,
    format: (estimate, options?: ActionOptions) => {
      return (
        <Box display="flex" gap={1}>
          <IconButton
            size="small"
            onClick={() => options?.onEdit?.(estimate)}
            title="Edit Estimate"
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => options?.onDelete?.(estimate)}
            title="Delete Estimate"
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      );
    },
  },
];
