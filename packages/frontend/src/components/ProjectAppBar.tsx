import {
  A<PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>,
  Container,
  <PERSON>,
  Typo<PERSON>,
  Box,
  Button,
} from '@mui/material';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import AnalyticsIcon from '@mui/icons-material/Analytics';

function ProjectAppBar() {
  const location = useLocation();

  const isActive = (path: string) => {
    return (
      location.pathname === path || location.pathname.startsWith(path + '/')
    );
  };

  return (
    <AppBar position="sticky">
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          <Box marginRight="10px">
            <Link component={RouterLink} to="/">
              <AnalyticsIcon sx={{ color: '#FFFFFF' }} />
            </Link>
          </Box>

          <Typography
            variant="h6"
            noWrap
            component="a"
            href="/"
            sx={{
              mr: 4,
              display: { xs: 'none', md: 'flex' },
              fontFamily: 'monospace',
              fontWeight: 700,
              letterSpacing: '.3rem',
              color: 'inherit',
              textDecoration: 'none',
            }}
          >
            PROJECT
          </Typography>

          <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' } }}>
            <Button
              component={RouterLink}
              to="/estimates"
              sx={{
                'my': 2,
                'color': 'white',
                'display': 'block',
                'textTransform': 'none',
                'backgroundColor': isActive('/estimates')
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              Estimates
            </Button>
            <Button
              component={RouterLink}
              to="/customers"
              sx={{
                'my': 2,
                'color': 'white',
                'display': 'block',
                'textTransform': 'none',
                'backgroundColor': isActive('/customers')
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              Customers
            </Button>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
}
export default ProjectAppBar;
