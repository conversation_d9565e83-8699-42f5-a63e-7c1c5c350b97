import CustomDrawer from '../CustomDrawer';
import CustomerForm from './CustomerForm';
import { useCreateCustomer, useUpdateCustomer, Customer } from './hooks';
import { useModal } from '../../context/modal';
import { useSnackbar } from '../../context/snackbar';
import Snackbar from '../Snackbar';

interface Props {
  customer?: Customer;
  resultHook?: {
    resolve: (customer?: Customer) => void;
    reject: () => void;
  };
}

export default function CustomerDrawer({ customer, resultHook }: Props) {
  const modal = useModal();
  const snackbar = useSnackbar();
  const createCustomerMutation = useCreateCustomer();
  const updateCustomerMutation = useUpdateCustomer();

  const isEditing = !!customer;

  const handleSubmit = async (values: any) => {
    try {
      let savedCustomer: Customer;

      if (isEditing) {
        savedCustomer = await updateCustomerMutation.mutateAsync({
          id: customer!.id,
          data: values,
        });
        snackbar.appoint(
          <Snackbar label="Customer updated successfully" type="success" />,
        );
      } else {
        savedCustomer = await createCustomerMutation.mutateAsync(values);
        snackbar.appoint(
          <Snackbar label="Customer created successfully" type="success" />,
        );
      }

      if (resultHook) {
        resultHook.resolve(savedCustomer);
      }

      modal.dismiss();
    } catch (error) {
      console.error(
        `Error ${isEditing ? 'updating' : 'creating'} customer:`,
        error,
      );
      snackbar.appoint(
        <Snackbar
          label={`Failed to ${isEditing ? 'update' : 'create'} customer`}
          type="error"
        />,
      );

      if (resultHook) {
        resultHook.reject();
      }
    }
  };

  return (
    <CustomDrawer
      label={customer ? 'Edit Customer' : 'New Customer'}
      width="800px"
    >
      <CustomerForm
        onSubmit={handleSubmit}
        initialValues={customer}
        isLoading={
          createCustomerMutation.isLoading || updateCustomerMutation.isLoading
        }
      />
    </CustomDrawer>
  );
}
