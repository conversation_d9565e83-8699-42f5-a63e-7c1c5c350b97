import { Formik } from 'formik';
import {
  Box,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Grid,
  Checkbox,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  LocationOn as LocationOnIcon,
} from '@mui/icons-material';
import { useModal } from '../../context/modal';
import FormActions from '../FormActions';

// Form field enums
enum FormField {
  // Name and contact fields
  Title = 'title',
  FirstName = 'firstName',
  MiddleName = 'middleName',
  LastName = 'lastName',
  Suffix = 'suffix',
  CompanyName = 'companyName',
  CustomerDisplayName = 'customerDisplayName',
  Email = 'email',
  PhoneNumber = 'phoneNumber',
  MobileNumber = 'mobileNumber',
  Fax = 'fax',
  Other = 'other',
  Website = 'website',
  NameToPrintOnChecks = 'nameToPrintOnChecks',

  // Billing address fields
  BillingStreetAddress1 = 'billingStreetAddress1',
  BillingStreetAddress2 = 'billingStreetAddress2',
  BillingCity = 'billingCity',
  BillingState = 'billingState',
  BillingZipCode = 'billingZipCode',
  BillingCountry = 'billingCountry',

  // Shipping address fields
  ShippingStreetAddress1 = 'shippingStreetAddress1',
  ShippingStreetAddress2 = 'shippingStreetAddress2',
  ShippingCity = 'shippingCity',
  ShippingState = 'shippingState',
  ShippingZipCode = 'shippingZipCode',
  ShippingCountry = 'shippingCountry',
  SameAsBillingAddress = 'sameAsBillingAddress',
}

interface FormValues {
  // Name and contact fields
  [FormField.Title]: string;
  [FormField.FirstName]: string;
  [FormField.MiddleName]: string;
  [FormField.LastName]: string;
  [FormField.Suffix]: string;
  [FormField.CompanyName]: string;
  [FormField.CustomerDisplayName]: string;
  [FormField.Email]: string;
  [FormField.PhoneNumber]: string;
  [FormField.MobileNumber]: string;
  [FormField.Fax]: string;
  [FormField.Other]: string;
  [FormField.Website]: string;
  [FormField.NameToPrintOnChecks]: string;

  // Billing address fields
  [FormField.BillingStreetAddress1]: string;
  [FormField.BillingStreetAddress2]: string;
  [FormField.BillingCity]: string;
  [FormField.BillingState]: string;
  [FormField.BillingZipCode]: string;
  [FormField.BillingCountry]: string;

  // Shipping address fields
  [FormField.ShippingStreetAddress1]: string;
  [FormField.ShippingStreetAddress2]: string;
  [FormField.ShippingCity]: string;
  [FormField.ShippingState]: string;
  [FormField.ShippingZipCode]: string;
  [FormField.ShippingCountry]: string;
  [FormField.SameAsBillingAddress]: boolean;
}

interface Props {
  onSubmit?: (values: FormValues) => void | Promise<void>;
  initialValues?: Partial<FormValues>;
  isLoading?: boolean;
}

interface FormErrors {
  general?: string;
  email?: string;
}

const validateForm = (values: FormValues): FormErrors => {
  const errors: FormErrors = {};

  // At least one name field is required
  if (
    !values.customerDisplayName &&
    !values.companyName &&
    !values.firstName &&
    !values.lastName
  ) {
    errors.general =
      'At least one of customer display name, company name, first name, or last name is required';
  }

  // Email validation
  if (values.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(values.email)) {
    errors.email = 'Invalid email format';
  }

  return errors;
};

export default function CustomerForm({
  onSubmit,
  initialValues,
  isLoading = false,
}: Props) {
  const modal = useModal();

  const defaultInitialValues: FormValues = {
    // Name and contact fields
    [FormField.Title]: '',
    [FormField.FirstName]: '',
    [FormField.MiddleName]: '',
    [FormField.LastName]: '',
    [FormField.Suffix]: '',
    [FormField.CompanyName]: '',
    [FormField.CustomerDisplayName]: '',
    [FormField.Email]: '',
    [FormField.PhoneNumber]: '',
    [FormField.MobileNumber]: '',
    [FormField.Fax]: '',
    [FormField.Other]: '',
    [FormField.Website]: '',
    [FormField.NameToPrintOnChecks]: '',

    // Billing address fields
    [FormField.BillingStreetAddress1]: '',
    [FormField.BillingStreetAddress2]: '',
    [FormField.BillingCity]: '',
    [FormField.BillingState]: '',
    [FormField.BillingZipCode]: '',
    [FormField.BillingCountry]: '',

    // Shipping address fields
    [FormField.ShippingStreetAddress1]: '',
    [FormField.ShippingStreetAddress2]: '',
    [FormField.ShippingCity]: '',
    [FormField.ShippingState]: '',
    [FormField.ShippingZipCode]: '',
    [FormField.ShippingCountry]: '',
    [FormField.SameAsBillingAddress]: false,
    ...initialValues,
  };

  return (
    <Formik<FormValues>
      initialValues={defaultInitialValues}
      validate={validateForm}
      validateOnChange={false}
      validateOnBlur={false}
      onSubmit={(values, { setSubmitting, setErrors }) => {
        const validationErrors = validateForm(values);
        if (Object.keys(validationErrors).length > 0) {
          setErrors(validationErrors as any);
          setSubmitting(false);
          return;
        }
        if (onSubmit) {
          onSubmit(values);
        }
        setSubmitting(false);
      }}
    >
      {({
        values,
        errors,
        touched,
        handleSubmit,
        setFieldValue,
        isSubmitting,
        isValid,
      }) => (
        <form
          onSubmit={handleSubmit}
          onKeyDown={(event: any) => {
            if (event.key === 'Escape') {
              modal.dismiss();
            }
          }}
          style={{ width: '100%' }}
        >
          {/* Error Message at Top */}
          {(errors as FormErrors).general && (
            <Box marginBottom="20px">
              <Typography color="error" variant="body2">
                {(errors as FormErrors).general}
              </Typography>
            </Box>
          )}

          {/* Name and Contact Accordion */}
          <Accordion defaultExpanded>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="name-contact-content"
              id="name-contact-header"
            >
              <Box display="flex" alignItems="center">
                <PersonIcon sx={{ marginRight: 1 }} />
                <Typography variant="h6">Name and contact</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                {/* First row: Title, First name, Middle name, Last name, Suffix */}
                <Grid item xs={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Title"
                    value={values.title}
                    onChange={(e) =>
                      setFieldValue(FormField.Title, e.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={2.5}>
                  <TextField
                    fullWidth
                    size="small"
                    label="First name"
                    value={values.firstName}
                    onChange={(e) =>
                      setFieldValue(FormField.FirstName, e.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={2.5}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Middle name"
                    value={values.middleName}
                    onChange={(e) =>
                      setFieldValue(FormField.MiddleName, e.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={2.5}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Last name"
                    value={values.lastName}
                    onChange={(e) =>
                      setFieldValue(FormField.LastName, e.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={2.5}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Suffix"
                    value={values.suffix}
                    onChange={(e) =>
                      setFieldValue(FormField.Suffix, e.target.value)
                    }
                  />
                </Grid>

                {/* Second row: Company name, Customer display name */}
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Company name"
                    value={values.companyName}
                    onChange={(e) =>
                      setFieldValue(FormField.CompanyName, e.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Customer display name *</InputLabel>
                    <Select
                      value={values.customerDisplayName}
                      label="Customer display name *"
                      onChange={(e) =>
                        setFieldValue(
                          FormField.CustomerDisplayName,
                          e.target.value,
                        )
                      }
                    >
                      <MenuItem value="">
                        <em>Select or enter custom</em>
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Third row: Email, Phone number */}
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Email"
                    type="email"
                    value={values.email}
                    onChange={(e) =>
                      setFieldValue(FormField.Email, e.target.value)
                    }
                    error={!!(errors as FormErrors).email}
                    helperText={(errors as FormErrors).email}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Phone number"
                    value={values.phoneNumber}
                    onChange={(e) =>
                      setFieldValue(FormField.PhoneNumber, e.target.value)
                    }
                  />
                </Grid>

                {/* Fourth row: Mobile number, Fax */}
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Mobile number"
                    value={values.mobileNumber}
                    onChange={(e) =>
                      setFieldValue(FormField.MobileNumber, e.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Fax"
                    value={values.fax}
                    onChange={(e) =>
                      setFieldValue(FormField.Fax, e.target.value)
                    }
                  />
                </Grid>

                {/* Fifth row: Other, Website */}
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Other"
                    value={values.other}
                    onChange={(e) =>
                      setFieldValue(FormField.Other, e.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Website"
                    value={values.website}
                    onChange={(e) =>
                      setFieldValue(FormField.Website, e.target.value)
                    }
                  />
                </Grid>

                {/* Sixth row: Name to print on checks */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Name to print on checks"
                    value={values.nameToPrintOnChecks}
                    onChange={(e) =>
                      setFieldValue(
                        FormField.NameToPrintOnChecks,
                        e.target.value,
                      )
                    }
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Addresses Accordion */}
          <Accordion defaultExpanded sx={{ marginTop: 2 }}>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="addresses-content"
              id="addresses-header"
            >
              <Box display="flex" alignItems="center">
                <LocationOnIcon sx={{ marginRight: 1 }} />
                <Typography variant="h6">Addresses</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box>
                {/* Billing Address Section */}
                <Typography
                  variant="subtitle1"
                  sx={{ marginBottom: 2, fontWeight: 'bold' }}
                >
                  Billing address
                </Typography>

                <Grid container spacing={2}>
                  {/* Street address 1 and 2 */}
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Street address 1"
                      value={values.billingStreetAddress1}
                      onChange={(e) =>
                        setFieldValue(
                          FormField.BillingStreetAddress1,
                          e.target.value,
                        )
                      }
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Street address 2"
                      value={values.billingStreetAddress2}
                      onChange={(e) =>
                        setFieldValue(
                          FormField.BillingStreetAddress2,
                          e.target.value,
                        )
                      }
                    />
                  </Grid>

                  {/* City and State */}
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      size="small"
                      label="City"
                      value={values.billingCity}
                      onChange={(e) =>
                        setFieldValue(FormField.BillingCity, e.target.value)
                      }
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      size="small"
                      label="State"
                      value={values.billingState}
                      onChange={(e) =>
                        setFieldValue(FormField.BillingState, e.target.value)
                      }
                    />
                  </Grid>

                  {/* ZIP code and Country */}
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      size="small"
                      label="ZIP code"
                      value={values.billingZipCode}
                      onChange={(e) =>
                        setFieldValue(FormField.BillingZipCode, e.target.value)
                      }
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Country"
                      value={values.billingCountry}
                      onChange={(e) =>
                        setFieldValue(FormField.BillingCountry, e.target.value)
                      }
                    />
                  </Grid>
                </Grid>

                {/* Shipping Address Section */}
                <Typography
                  variant="subtitle1"
                  sx={{ marginTop: 3, marginBottom: 2, fontWeight: 'bold' }}
                >
                  Shipping address
                </Typography>

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={values.sameAsBillingAddress}
                      onChange={(e) => {
                        const checked = e.target.checked;
                        setFieldValue(FormField.SameAsBillingAddress, checked);

                        // If checked, copy billing address to shipping address
                        if (checked) {
                          setFieldValue(
                            FormField.ShippingStreetAddress1,
                            values.billingStreetAddress1,
                          );
                          setFieldValue(
                            FormField.ShippingStreetAddress2,
                            values.billingStreetAddress2,
                          );
                          setFieldValue(
                            FormField.ShippingCity,
                            values.billingCity,
                          );
                          setFieldValue(
                            FormField.ShippingState,
                            values.billingState,
                          );
                          setFieldValue(
                            FormField.ShippingZipCode,
                            values.billingZipCode,
                          );
                          setFieldValue(
                            FormField.ShippingCountry,
                            values.billingCountry,
                          );
                        }
                      }}
                    />
                  }
                  label="Same as billing address"
                />

                {!values.sameAsBillingAddress && (
                  <Grid container spacing={2} sx={{ marginTop: 1 }}>
                    {/* Street address 1 and 2 */}
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Street address 1"
                        value={values.shippingStreetAddress1}
                        onChange={(e) =>
                          setFieldValue(
                            FormField.ShippingStreetAddress1,
                            e.target.value,
                          )
                        }
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Street address 2"
                        value={values.shippingStreetAddress2}
                        onChange={(e) =>
                          setFieldValue(
                            FormField.ShippingStreetAddress2,
                            e.target.value,
                          )
                        }
                      />
                    </Grid>

                    {/* City and State */}
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="City"
                        value={values.shippingCity}
                        onChange={(e) =>
                          setFieldValue(FormField.ShippingCity, e.target.value)
                        }
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="State"
                        value={values.shippingState}
                        onChange={(e) =>
                          setFieldValue(FormField.ShippingState, e.target.value)
                        }
                      />
                    </Grid>

                    {/* ZIP code and Country */}
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="ZIP code"
                        value={values.shippingZipCode}
                        onChange={(e) =>
                          setFieldValue(
                            FormField.ShippingZipCode,
                            e.target.value,
                          )
                        }
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Country"
                        value={values.shippingCountry}
                        onChange={(e) =>
                          setFieldValue(
                            FormField.ShippingCountry,
                            e.target.value,
                          )
                        }
                      />
                    </Grid>
                  </Grid>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>

          <FormActions
            label="Save Customer"
            onSubmit={() => handleSubmit()}
            isDisabled={!isValid || isSubmitting || isLoading}
            isLoading={isSubmitting || isLoading}
          />
        </form>
      )}
    </Formik>
  );
}
