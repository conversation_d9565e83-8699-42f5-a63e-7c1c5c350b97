import { Helmet } from 'react-helmet-async';
import TableHeader from '../TableHeader';
import CustomerTable from '../CustomerTable';
import { useModal } from '../../context/modal';
import CustomerDrawer from './CustomerDrawer';

export default function Customers() {
  const modal = useModal();

  const handleCreateCustomer = async () => {
    try {
      await modal.appoint(<CustomerDrawer />);
    } catch (error) {
      // User cancelled or error occurred
      console.log('Customer creation cancelled or failed');
    }
  };

  return (
    <>
      <Helmet>
        <title>Customers | Project</title>
      </Helmet>

      <TableHeader
        label="Customers"
        createLabel="New Customer"
        onCreate={handleCreateCustomer}
      />

      <CustomerTable />
    </>
  );
}
