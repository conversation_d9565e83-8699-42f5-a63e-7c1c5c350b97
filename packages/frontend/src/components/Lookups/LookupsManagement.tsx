import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { Helmet } from 'react-helmet-async';
import {
  useLookupCategories,
  useLookupItems,
  useUpdateLookupCategoriesOrder,
  useUpdateLookupItemsOrder,
  useDeleteLookupCategory,
  useDeleteLookupItem,
} from './hooks';
import { useModal } from '../../context/modal';
import { useSnackbar } from '../../context/snackbar';
import Snackbar from '../Snackbar';
import LookupCategoryForm from './LookupCategoryForm';
import LookupItemForm from './LookupItemForm';
import { LookupCategory, LookupItem } from '../../interfaces';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`lookup-tabpanel-${index}`}
      aria-labelledby={`lookup-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `lookup-tab-${index}`,
    'aria-controls': `lookup-tabpanel-${index}`,
  };
}

export default function LookupsManagement() {
  const [tabValue, setTabValue] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<LookupCategory | null>(null);

  const modal = useModal();
  const snackbar = useSnackbar();

  const { data: categories = [], isLoading: categoriesLoading, isError: categoriesError } = useLookupCategories();
  const { data: items = [], isLoading: itemsLoading, isError: itemsError } = useLookupItems();

  const updateCategoriesOrderMutation = useUpdateLookupCategoriesOrder();
  const updateItemsOrderMutation = useUpdateLookupItemsOrder();
  const deleteCategoryMutation = useDeleteLookupCategory();
  const deleteItemMutation = useDeleteLookupItem();

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setSelectedCategory(null);
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination, type } = result;

    if (source.index === destination.index) return;

    if (type === 'category') {
      const reorderedCategories = Array.from(categories);
      const [removed] = reorderedCategories.splice(source.index, 1);
      reorderedCategories.splice(destination.index, 0, removed);

      const reorderPayload = reorderedCategories.map((category, index) => ({
        id: category.id,
        sortOrder: index,
      }));

      updateCategoriesOrderMutation.mutate(reorderPayload, {
        onSuccess: () => {
          snackbar.appoint(<Snackbar label="Categories reordered successfully" type="success" />);
        },
        onError: () => {
          snackbar.appoint(<Snackbar label="Failed to reorder categories" type="error" />);
        },
      });
    } else if (type === 'item') {
      const categoryItems = selectedCategory?.items || [];
      const reorderedItems = Array.from(categoryItems);
      const [removed] = reorderedItems.splice(source.index, 1);
      reorderedItems.splice(destination.index, 0, removed);

      const reorderPayload = reorderedItems.map((item, index) => ({
        id: item.id,
        sortOrder: index,
      }));

      updateItemsOrderMutation.mutate(reorderPayload, {
        onSuccess: () => {
          snackbar.appoint(<Snackbar label="Items reordered successfully" type="success" />);
        },
        onError: () => {
          snackbar.appoint(<Snackbar label="Failed to reorder items" type="error" />);
        },
      });
    }
  };

  const handleCreateCategory = async () => {
    try {
      await modal.appoint(<LookupCategoryForm />);
      snackbar.appoint(<Snackbar label="Category created successfully" type="success" />);
    } catch (error) {
      // User cancelled or error occurred
    }
  };

  const handleEditCategory = async (category: LookupCategory) => {
    try {
      await modal.appoint(<LookupCategoryForm category={category} />);
      snackbar.appoint(<Snackbar label="Category updated successfully" type="success" />);
    } catch (error) {
      // User cancelled or error occurred
    }
  };

  const handleDeleteCategory = async (category: LookupCategory) => {
    if (window.confirm(`Are you sure you want to delete "${category.name}"? This will also delete all items in this category.`)) {
      deleteCategoryMutation.mutate(category.id, {
        onSuccess: () => {
          snackbar.appoint(<Snackbar label="Category deleted successfully" type="success" />);
          if (selectedCategory?.id === category.id) {
            setSelectedCategory(null);
          }
        },
        onError: () => {
          snackbar.appoint(<Snackbar label="Failed to delete category" type="error" />);
        },
      });
    }
  };

  const handleCreateItem = async () => {
    if (!selectedCategory) return;

    try {
      await modal.appoint(<LookupItemForm categoryId={selectedCategory.id} />);
      snackbar.appoint(<Snackbar label="Item created successfully" type="success" />);
    } catch (error) {
      // User cancelled or error occurred
    }
  };

  const handleEditItem = async (item: LookupItem) => {
    try {
      await modal.appoint(<LookupItemForm item={item} categoryId={item.categoryId} />);
      snackbar.appoint(<Snackbar label="Item updated successfully" type="success" />);
    } catch (error) {
      // User cancelled or error occurred
    }
  };

  const handleDeleteItem = async (item: LookupItem) => {
    if (window.confirm(`Are you sure you want to delete "${item.name}"? This will also delete all dependent items.`)) {
      deleteItemMutation.mutate(item.id, {
        onSuccess: () => {
          snackbar.appoint(<Snackbar label="Item deleted successfully" type="success" />);
        },
        onError: () => {
          snackbar.appoint(<Snackbar label="Failed to delete item" type="error" />);
        },
      });
    }
  };

  if (categoriesLoading || itemsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (categoriesError || itemsError) {
    return (
      <Box margin="20px">
        <Alert severity="error">
          Failed to load lookup data. Please try refreshing the page.
        </Alert>
      </Box>
    );
  }

  return (
    <Box margin="20px">
      <Helmet>
        <title>Lookups Management | Project</title>
      </Helmet>

      <Box marginBottom="20px">
        <Typography variant="h4" gutterBottom>
          Lookups Management
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Configure lookup categories and items for estimates. Use drag and drop to reorder items.
        </Typography>
      </Box>

      <Paper>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="lookup tabs">
            <Tab
              icon={<SettingsIcon />}
              label="Categories"
              {...a11yProps(0)}
              sx={{ minHeight: 48 }}
            />
            <Tab
              icon={<SettingsIcon />}
              label="Items"
              {...a11yProps(1)}
              sx={{ minHeight: 48 }}
            />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="20px">
            <Typography variant="h6">Lookup Categories</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateCategory}
            >
              Add Category
            </Button>
          </Box>

          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="categories" type="category">
              {(provided) => (
                <List {...provided.droppableProps} ref={provided.innerRef}>
                  {categories.map((category, index) => (
                    <Draggable
                      key={category.id}
                      draggableId={`category-${category.id}`}
                      index={index}
                    >
                      {(provided, snapshot) => (
                        <ListItem
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          sx={{
                            backgroundColor: snapshot.isDragging ? '#f5f5f5' : 'transparent',
                            border: snapshot.isDragging ? '1px solid #ccc' : 'none',
                            borderRadius: snapshot.isDragging ? 1 : 0,
                            marginBottom: snapshot.isDragging ? 1 : 0,
                          }}
                        >
                          <Box {...provided.dragHandleProps} sx={{ marginRight: 1 }}>
                            <DragIcon color="action" />
                          </Box>
                          <ListItemText
                            primary={category.name}
                            secondary={
                              <Box display="flex" alignItems="center" gap={1}>
                                <span>{category.description}</span>
                                <Chip
                                  label={`${category.items?.length || 0} items`}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                />
                              </Box>
                            }
                          />
                          <ListItemSecondaryAction>
                            <IconButton
                              edge="end"
                              onClick={() => handleEditCategory(category)}
                              sx={{ marginRight: 1 }}
                            >
                              <EditIcon />
                            </IconButton>
                            <IconButton
                              edge="end"
                              onClick={() => handleDeleteCategory(category)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </List>
              )}
            </Droppable>
          </DragDropContext>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box display="flex" gap={2}>
            {/* Categories List */}
            <Paper variant="outlined" sx={{ width: 300, padding: 2 }}>
              <Typography variant="h6" gutterBottom>
                Select Category
              </Typography>
              <List dense>
                {categories.map((category) => (
                  <ListItem
                    key={category.id}
                    button
                    selected={selectedCategory?.id === category.id}
                    onClick={() => setSelectedCategory(category)}
                  >
                    <ListItemText
                      primary={category.name}
                      secondary={`${category.items?.length || 0} items`}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>

            {/* Items List */}
            <Box flex={1}>
              {selectedCategory ? (
                <>
                  <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="20px">
                    <Typography variant="h6">
                      Items in "{selectedCategory.name}"
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={handleCreateItem}
                    >
                      Add Item
                    </Button>
                  </Box>

                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="items" type="item">
                      {(provided) => (
                        <List {...provided.droppableProps} ref={provided.innerRef}>
                          {(selectedCategory.items || []).map((item, index) => (
                            <Draggable
                              key={item.id}
                              draggableId={`item-${item.id}`}
                              index={index}
                            >
                              {(provided, snapshot) => (
                                <ListItem
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  sx={{
                                    backgroundColor: snapshot.isDragging ? '#f5f5f5' : 'transparent',
                                    border: snapshot.isDragging ? '1px solid #ccc' : 'none',
                                    borderRadius: snapshot.isDragging ? 1 : 0,
                                    marginBottom: snapshot.isDragging ? 1 : 0,
                                  }}
                                >
                                  <Box {...provided.dragHandleProps} sx={{ marginRight: 1 }}>
                                    <DragIcon color="action" />
                                  </Box>
                                  <ListItemText
                                    primary={item.name}
                                    secondary={
                                      <Box display="flex" alignItems="center" gap={1}>
                                        <span>Value: {item.value}</span>
                                        {item.parent && (
                                          <Chip
                                            label={`Depends on: ${item.parent.name}`}
                                            size="small"
                                            color="secondary"
                                            variant="outlined"
                                          />
                                        )}
                                        {item.children && item.children.length > 0 && (
                                          <Chip
                                            label={`${item.children.length} dependent items`}
                                            size="small"
                                            color="info"
                                            variant="outlined"
                                          />
                                        )}
                                      </Box>
                                    }
                                  />
                                  <ListItemSecondaryAction>
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleEditItem(item)}
                                      sx={{ marginRight: 1 }}
                                    >
                                      <EditIcon />
                                    </IconButton>
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleDeleteItem(item)}
                                      color="error"
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </ListItemSecondaryAction>
                                </ListItem>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </List>
                      )}
                    </Droppable>
                  </DragDropContext>
                </>
              ) : (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  minHeight="300px"
                  color="text.secondary"
                >
                  <Typography variant="h6">
                    Select a category to view and manage its items
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </TabPanel>
      </Paper>
    </Box>
  );
}
