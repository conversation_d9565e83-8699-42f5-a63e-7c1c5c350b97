import React from 'react';
import { Formik } from 'formik';
import {
  Box,
  TextField,
  FormControlLabel,
  Switch,
  Typography,
  Alert,
} from '@mui/material';
import { useModal } from '../../context/modal';
import FormActions from '../FormActions';
import { useCreateLookupCategory, useUpdateLookupCategory } from './hooks';
import { LookupCategory, CreateLookupCategoryPayload, UpdateLookupCategoryPayload } from '../../interfaces';

interface Props {
  category?: LookupCategory;
}

interface FormValues {
  name: string;
  description: string;
  isActive: boolean;
}

interface FormErrors {
  name?: string;
  description?: string;
  general?: string;
}

const validateForm = (values: FormValues): FormErrors => {
  const errors: FormErrors = {};

  if (!values.name || values.name.trim() === '') {
    errors.name = 'Category name is required';
  }

  return errors;
};

export default function LookupCategoryForm({ category }: Props) {
  const modal = useModal();
  const createCategoryMutation = useCreateLookupCategory();
  const updateCategoryMutation = useUpdateLookupCategory();

  const isEditing = !!category;

  const initialValues: FormValues = {
    name: category?.name || '',
    description: category?.description || '',
    isActive: category?.isActive ?? true,
  };

  const handleSubmit = async (values: FormValues) => {
    try {
      if (isEditing) {
        const payload: UpdateLookupCategoryPayload = {
          name: values.name.trim(),
          description: values.description.trim() || undefined,
          isActive: values.isActive,
        };
        await updateCategoryMutation.mutateAsync({ id: category!.id, payload });
      } else {
        const payload: CreateLookupCategoryPayload = {
          name: values.name.trim(),
          description: values.description.trim() || undefined,
          isActive: values.isActive,
        };
        await createCategoryMutation.mutateAsync(payload);
      }
      modal.resolve();
    } catch (error) {
      console.error('Error saving category:', error);
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validate={validateForm}
      onSubmit={handleSubmit}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        isSubmitting,
        setFieldValue,
      }) => (
        <form onSubmit={handleSubmit} style={{ width: '500px' }}>
          <Box marginBottom="20px">
            <Typography variant="h6" gutterBottom>
              {isEditing ? 'Edit Category' : 'Create Category'}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {isEditing
                ? 'Update the category information below.'
                : 'Create a new lookup category for organizing items.'}
            </Typography>
          </Box>

          {(createCategoryMutation.isError || updateCategoryMutation.isError) && (
            <Box marginBottom="20px">
              <Alert severity="error">
                {createCategoryMutation.error?.message ||
                  updateCategoryMutation.error?.message ||
                  'An error occurred while saving the category.'}
              </Alert>
            </Box>
          )}

          <Box marginBottom="20px">
            <TextField
              fullWidth
              label="Category Name"
              name="name"
              value={values.name}
              onChange={handleChange}
              onBlur={handleBlur}
              error={!!(errors.name && touched.name)}
              helperText={errors.name && touched.name ? errors.name : ''}
              placeholder="e.g., Line Item Types, Labor Items"
              required
            />
          </Box>

          <Box marginBottom="20px">
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Description"
              name="description"
              value={values.description}
              onChange={handleChange}
              onBlur={handleBlur}
              error={!!(errors.description && touched.description)}
              helperText={errors.description && touched.description ? errors.description : ''}
              placeholder="Optional description of this category"
            />
          </Box>

          <Box marginBottom="20px">
            <FormControlLabel
              control={
                <Switch
                  checked={values.isActive}
                  onChange={(e) => setFieldValue('isActive', e.target.checked)}
                  name="isActive"
                />
              }
              label="Active"
            />
            <Typography variant="body2" color="textSecondary" sx={{ marginLeft: 4 }}>
              Inactive categories and their items will not be available for selection
            </Typography>
          </Box>

          <FormActions
            label={isEditing ? 'Update Category' : 'Create Category'}
            onCancel={() => modal.dismiss()}
            onSubmit={handleSubmit}
            isDisabled={isSubmitting || createCategoryMutation.isLoading || updateCategoryMutation.isLoading}
            isLoading={isSubmitting || createCategoryMutation.isLoading || updateCategoryMutation.isLoading}
          />
        </form>
      )}
    </Formik>
  );
}
