import React from 'react';
import { Formik } from 'formik';
import {
  Box,
  TextField,
  FormControlLabel,
  Switch,
  Typography,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { useModal } from '../../context/modal';
import FormActions from '../FormActions';
import {
  useCreateLookupItem,
  useUpdateLookupItem,
  useLookupCategories,
  useLookupItems,
} from './hooks';
import {
  LookupItem,
  CreateLookupItemPayload,
  UpdateLookupItemPayload,
} from '../../interfaces';

interface Props {
  item?: LookupItem;
  categoryId: number;
}

interface FormValues {
  categoryId: number;
  parentId: number | '';
  name: string;
  value: string;
  description: string;
  isActive: boolean;
}

interface FormErrors {
  categoryId?: string;
  name?: string;
  value?: string;
  description?: string;
  general?: string;
}

const validateForm = (values: FormValues): FormErrors => {
  const errors: FormErrors = {};

  if (!values.categoryId) {
    errors.categoryId = 'Category is required';
  }

  if (!values.name || values.name.trim() === '') {
    errors.name = 'Item name is required';
  }

  if (!values.value || values.value.trim() === '') {
    errors.value = 'Item value is required';
  }

  return errors;
};

export default function LookupItemForm({ item, categoryId }: Props) {
  const modal = useModal();
  const createItemMutation = useCreateLookupItem();
  const updateItemMutation = useUpdateLookupItem();

  const { data: categories = [] } = useLookupCategories();
  const { data: allItems = [] } = useLookupItems();

  const isEditing = !!item;

  const initialValues: FormValues = {
    categoryId: item?.categoryId || categoryId,
    parentId: item?.parentId || '',
    name: item?.name || '',
    value: item?.value || '',
    description: item?.description || '',
    isActive: item?.isActive ?? true,
  };

  // Get potential parent items (items that could be parents for this item)
  const getPotentialParents = (selectedCategoryId: number) => {
    return allItems.filter(
      (potentialParent) =>
        potentialParent.id !== item?.id && // Can't be parent of itself
        !potentialParent.parentId && // Only top-level items can be parents
        potentialParent.isActive // Only active items
    );
  };

  const handleSubmit = async (values: FormValues) => {
    try {
      if (isEditing) {
        const payload: UpdateLookupItemPayload = {
          categoryId: values.categoryId,
          parentId: values.parentId || undefined,
          name: values.name.trim(),
          value: values.value.trim(),
          description: values.description.trim() || undefined,
          isActive: values.isActive,
        };
        await updateItemMutation.mutateAsync({ id: item!.id, payload });
      } else {
        const payload: CreateLookupItemPayload = {
          categoryId: values.categoryId,
          parentId: values.parentId || undefined,
          name: values.name.trim(),
          value: values.value.trim(),
          description: values.description.trim() || undefined,
          isActive: values.isActive,
        };
        await createItemMutation.mutateAsync(payload);
      }
      modal.resolve();
    } catch (error) {
      console.error('Error saving item:', error);
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validate={validateForm}
      onSubmit={handleSubmit}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        isSubmitting,
        setFieldValue,
      }) => (
        <form onSubmit={handleSubmit} style={{ width: '500px' }}>
          <Box marginBottom="20px">
            <Typography variant="h6" gutterBottom>
              {isEditing ? 'Edit Item' : 'Create Item'}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {isEditing
                ? 'Update the item information below.'
                : 'Create a new lookup item.'}
            </Typography>
          </Box>

          {(createItemMutation.isError || updateItemMutation.isError) && (
            <Box marginBottom="20px">
              <Alert severity="error">
                {createItemMutation.error?.message ||
                  updateItemMutation.error?.message ||
                  'An error occurred while saving the item.'}
              </Alert>
            </Box>
          )}

          <Box marginBottom="20px">
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={values.categoryId}
                onChange={(e) => {
                  setFieldValue('categoryId', e.target.value);
                  setFieldValue('parentId', ''); // Reset parent when category changes
                }}
                error={!!(errors.categoryId && touched.categoryId)}
                label="Category"
              >
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          <Box marginBottom="20px">
            <FormControl fullWidth>
              <InputLabel>Parent Item (Optional)</InputLabel>
              <Select
                value={values.parentId}
                onChange={(e) => setFieldValue('parentId', e.target.value)}
                label="Parent Item (Optional)"
              >
                <MenuItem value="">
                  <em>None (Top-level item)</em>
                </MenuItem>
                {getPotentialParents(values.categoryId).map((parentItem) => (
                  <MenuItem key={parentItem.id} value={parentItem.id}>
                    {parentItem.name} ({parentItem.value})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Typography variant="body2" color="textSecondary" sx={{ marginTop: 1 }}>
              Select a parent item to create a dependent lookup (e.g., "Digout" depends on "Labor")
            </Typography>
          </Box>

          <Box marginBottom="20px">
            <TextField
              fullWidth
              label="Item Name"
              name="name"
              value={values.name}
              onChange={handleChange}
              onBlur={handleBlur}
              error={!!(errors.name && touched.name)}
              helperText={errors.name && touched.name ? errors.name : ''}
              placeholder="e.g., Labor, Digout, Asphalt"
              required
            />
          </Box>

          <Box marginBottom="20px">
            <TextField
              fullWidth
              label="Item Value"
              name="value"
              value={values.value}
              onChange={handleChange}
              onBlur={handleBlur}
              error={!!(errors.value && touched.value)}
              helperText={
                errors.value && touched.value
                  ? errors.value
                  : 'The value used internally by the system'
              }
              placeholder="e.g., Labor, Digout, Asphalt"
              required
            />
          </Box>

          <Box marginBottom="20px">
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Description"
              name="description"
              value={values.description}
              onChange={handleChange}
              onBlur={handleBlur}
              error={!!(errors.description && touched.description)}
              helperText={errors.description && touched.description ? errors.description : ''}
              placeholder="Optional description of this item"
            />
          </Box>

          <Box marginBottom="20px">
            <FormControlLabel
              control={
                <Switch
                  checked={values.isActive}
                  onChange={(e) => setFieldValue('isActive', e.target.checked)}
                  name="isActive"
                />
              }
              label="Active"
            />
            <Typography variant="body2" color="textSecondary" sx={{ marginLeft: 4 }}>
              Inactive items will not be available for selection
            </Typography>
          </Box>

          <FormActions
            label={isEditing ? 'Update Item' : 'Create Item'}
            onCancel={() => modal.dismiss()}
            onSubmit={handleSubmit}
            isDisabled={isSubmitting || createItemMutation.isLoading || updateItemMutation.isLoading}
            isLoading={isSubmitting || createItemMutation.isLoading || updateItemMutation.isLoading}
          />
        </form>
      )}
    </Formik>
  );
}
