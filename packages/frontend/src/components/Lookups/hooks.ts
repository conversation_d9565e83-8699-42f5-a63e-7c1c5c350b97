import { useMutation, useQuery, useQueryClient } from 'react-query';
import useProjectClient from '../../hooks/useProjectClient';
import {
  LookupCategory,
  LookupItem,
  CreateLookupCategoryPayload,
  UpdateLookupCategoryPayload,
  CreateLookupItemPayload,
  UpdateLookupItemPayload,
  ReorderPayload,
} from '../../interfaces';

// Lookup Categories
export function useLookupCategories() {
  const client = useProjectClient();

  return useQuery<LookupCategory[], Error>('lookup-categories', () => client.getLookupCategories(), {
    onError: (error) => {
      console.error('Error fetching lookup categories:', error);
    },
  });
}

export function useLookupCategory(id: number) {
  const client = useProjectClient();

  return useQuery<LookupCategory, Error>(
    ['lookup-category', id],
    () => client.getLookupCategory(id),
    {
      enabled: !!id,
      onError: (error) => {
        console.error('Error fetching lookup category:', error);
      },
    }
  );
}

export function useCreateLookupCategory() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (payload: CreateLookupCategoryPayload) => {
      return client.createLookupCategory(payload);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('lookup-categories');
      },
      onError: (error) => {
        console.error('Error creating lookup category:', error);
      },
    }
  );
}

export function useUpdateLookupCategory() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async ({ id, payload }: { id: number; payload: UpdateLookupCategoryPayload }) => {
      return client.updateLookupCategory(id, payload);
    },
    {
      onSuccess: (_, { id }) => {
        queryClient.invalidateQueries('lookup-categories');
        queryClient.invalidateQueries(['lookup-category', id]);
      },
      onError: (error) => {
        console.error('Error updating lookup category:', error);
      },
    }
  );
}

export function useDeleteLookupCategory() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (id: number) => {
      return client.deleteLookupCategory(id);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('lookup-categories');
      },
      onError: (error) => {
        console.error('Error deleting lookup category:', error);
      },
    }
  );
}

export function useUpdateLookupCategoriesOrder() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (categories: ReorderPayload[]) => {
      return client.updateLookupCategoriesOrder({ categories });
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('lookup-categories');
      },
      onError: (error) => {
        console.error('Error updating lookup categories order:', error);
      },
    }
  );
}

// Lookup Items
export function useLookupItems() {
  const client = useProjectClient();

  return useQuery<LookupItem[], Error>('lookup-items', () => client.getLookupItems(), {
    onError: (error) => {
      console.error('Error fetching lookup items:', error);
    },
  });
}

export function useLookupItem(id: number) {
  const client = useProjectClient();

  return useQuery<LookupItem, Error>(
    ['lookup-item', id],
    () => client.getLookupItem(id),
    {
      enabled: !!id,
      onError: (error) => {
        console.error('Error fetching lookup item:', error);
      },
    }
  );
}

export function useLookupItemsByCategory(categoryId: number) {
  const client = useProjectClient();

  return useQuery<LookupItem[], Error>(
    ['lookup-items-by-category', categoryId],
    () => client.getLookupItemsByCategory(categoryId),
    {
      enabled: !!categoryId,
      onError: (error) => {
        console.error('Error fetching lookup items by category:', error);
      },
    }
  );
}

export function useLookupItemsByParent(parentId: number) {
  const client = useProjectClient();

  return useQuery<LookupItem[], Error>(
    ['lookup-items-by-parent', parentId],
    () => client.getLookupItemsByParent(parentId),
    {
      enabled: !!parentId,
      onError: (error) => {
        console.error('Error fetching lookup items by parent:', error);
      },
    }
  );
}

export function useCreateLookupItem() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (payload: CreateLookupItemPayload) => {
      return client.createLookupItem(payload);
    },
    {
      onSuccess: (_, payload) => {
        queryClient.invalidateQueries('lookup-items');
        queryClient.invalidateQueries(['lookup-items-by-category', payload.categoryId]);
        if (payload.parentId) {
          queryClient.invalidateQueries(['lookup-items-by-parent', payload.parentId]);
        }
      },
      onError: (error) => {
        console.error('Error creating lookup item:', error);
      },
    }
  );
}

export function useUpdateLookupItem() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async ({ id, payload }: { id: number; payload: UpdateLookupItemPayload }) => {
      return client.updateLookupItem(id, payload);
    },
    {
      onSuccess: (_, { id, payload }) => {
        queryClient.invalidateQueries('lookup-items');
        queryClient.invalidateQueries(['lookup-item', id]);
        if (payload.categoryId) {
          queryClient.invalidateQueries(['lookup-items-by-category', payload.categoryId]);
        }
        if (payload.parentId) {
          queryClient.invalidateQueries(['lookup-items-by-parent', payload.parentId]);
        }
      },
      onError: (error) => {
        console.error('Error updating lookup item:', error);
      },
    }
  );
}

export function useDeleteLookupItem() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (id: number) => {
      return client.deleteLookupItem(id);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('lookup-items');
        queryClient.invalidateQueries('lookup-items-by-category');
        queryClient.invalidateQueries('lookup-items-by-parent');
      },
      onError: (error) => {
        console.error('Error deleting lookup item:', error);
      },
    }
  );
}

export function useUpdateLookupItemsOrder() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async (items: ReorderPayload[]) => {
      return client.updateLookupItemsOrder({ items });
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('lookup-items');
        queryClient.invalidateQueries('lookup-items-by-category');
        queryClient.invalidateQueries('lookup-items-by-parent');
      },
      onError: (error) => {
        console.error('Error updating lookup items order:', error);
      },
    }
  );
}

// Helper hooks for estimate form
export function useLineItemTypes() {
  const client = useProjectClient();

  return useQuery<LookupItem[], Error>('line-item-types', () => client.getLineItemTypes(), {
    onError: (error) => {
      console.error('Error fetching line item types:', error);
    },
  });
}

export function useItemsForLineItemType(lineItemType: string) {
  const client = useProjectClient();

  return useQuery<LookupItem[], Error>(
    ['items-for-line-item-type', lineItemType],
    () => client.getItemsForLineItemType(lineItemType),
    {
      enabled: !!lineItemType,
      onError: (error) => {
        console.error('Error fetching items for line item type:', error);
      },
    }
  );
}
