export interface LookupCategory {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  items?: LookupItem[];
}

export interface LookupItem {
  id: number;
  categoryId: number;
  parentId?: number;
  name: string;
  value: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  category?: LookupCategory;
  parent?: LookupItem;
  children?: LookupItem[];
}

export interface CreateLookupCategoryPayload {
  name: string;
  description?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateLookupCategoryPayload {
  name?: string;
  description?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface CreateLookupItemPayload {
  categoryId: number;
  parentId?: number;
  name: string;
  value: string;
  description?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateLookupItemPayload {
  categoryId?: number;
  parentId?: number;
  name?: string;
  value?: string;
  description?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface ReorderPayload {
  id: number;
  sortOrder: number;
}
