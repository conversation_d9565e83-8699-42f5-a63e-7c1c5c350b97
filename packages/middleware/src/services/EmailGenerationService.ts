import { EmailTemplateService, EmailTemplateData } from './EmailTemplateService';

export interface EmailGenerationOptions {
  templateName?: string;
  subject?: string;
  recipientEmail?: string;
  recipientName?: string;
}

export interface EmailPreviewData {
  subject: string;
  recipientEmail: string;
  recipientName: string;
  htmlContent: string;
  textContent: string;
}

export class EmailGenerationService {
  private static instance: EmailGenerationService;
  private templateService: EmailTemplateService;

  private constructor() {
    this.templateService = EmailTemplateService.getInstance();
  }

  public static getInstance(): EmailGenerationService {
    if (!EmailGenerationService.instance) {
      EmailGenerationService.instance = new EmailGenerationService();
    }
    return EmailGenerationService.instance;
  }

  public generateEstimateEmailPreview(
    estimateId: number,
    data: EmailTemplateData,
    options: EmailGenerationOptions = {}
  ): EmailPreviewData {
    // Set default options
    const templateName = options.templateName || 'default-estimate-email';
    const subject = options.subject || `Estimate #${estimateId} from PROJECT`;
    const recipientEmail = options.recipientEmail || data.customer.email || '<EMAIL>';
    const recipientName = options.recipientName || this.getCustomerName(data.customer);

    // Generate HTML content from template
    const htmlContent = this.templateService.renderTemplate(templateName, data);

    // Generate plain text version (simplified)
    const textContent = this.generateTextContent(data);

    return {
      subject,
      recipientEmail,
      recipientName,
      htmlContent,
      textContent,
    };
  }

  private getCustomerName(customer: any): string {
    if (customer.customerDisplayName) {
      return customer.customerDisplayName;
    }
    if (customer.companyName) {
      return customer.companyName;
    }
    const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
    return nameParts.length > 0 ? nameParts.join(' ') : 'Customer';
  }

  private generateTextContent(data: EmailTemplateData): string {
    const customerName = this.getCustomerName(data.customer);
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    };

    const formatDate = (date: Date) => {
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }).format(new Date(date));
    };

    let textContent = `Dear ${customerName},

Thank you for your interest in our services. Please find your estimate details below:

ESTIMATE SUMMARY
================
Estimate #: ${data.estimate.id}`;

    if (data.estimate.name) {
      textContent += `\nProject: ${data.estimate.name}`;
    }

    if (data.estimate.description) {
      textContent += `\nDescription: ${data.estimate.description}`;
    }

    textContent += `\nDate: ${formatDate(data.estimate.createdAt)}
Total Amount: ${formatCurrency(data.totalPrice)}

BILL TO
=======
${customerName}`;

    if (data.customer.email) {
      textContent += `\n${data.customer.email}`;
    }

    if (data.customer.phoneNumber) {
      textContent += `\n${data.customer.phoneNumber}`;
    }

    // Add address
    const addressLines = [];
    if (data.customer.billingStreetAddress1) {
      addressLines.push(data.customer.billingStreetAddress1);
    }
    if (data.customer.billingStreetAddress2) {
      addressLines.push(data.customer.billingStreetAddress2);
    }
    const cityStateZip = [
      data.customer.billingCity,
      data.customer.billingState,
      data.customer.billingZipCode
    ].filter(Boolean).join(', ');
    if (cityStateZip) {
      addressLines.push(cityStateZip);
    }
    if (data.customer.billingCountry && data.customer.billingCountry !== 'US' && data.customer.billingCountry !== 'USA') {
      addressLines.push(data.customer.billingCountry);
    }
    if (addressLines.length > 0) {
      textContent += `\n${addressLines.join('\n')}`;
    }

    textContent += `\n\nLINE ITEMS
==========`;

    data.lineItems.forEach((item) => {
      textContent += `\n${item.type} - ${item.item}`;
      textContent += `\n  Units: ${item.units}`;
      if (item.time) {
        textContent += ` | Time: ${item.time}`;
      }
      textContent += ` | Rate: ${formatCurrency(item.rate)} | Price: ${formatCurrency(item.price)}`;
    });

    textContent += `\n\nTOTAL: ${formatCurrency(data.totalPrice)}

If you have any questions about this estimate, please don't hesitate to contact us.

We look forward to working with you!

Best regards,
PROJECT Team

---
PROJECT
Email: <EMAIL> | Phone: (*************
Visit us at: www.project.com

This estimate is valid for 30 days from the date issued.`;

    return textContent;
  }

  // Future enhancement: Actual email sending functionality
  public async sendEstimateEmail(
    estimateId: number,
    data: EmailTemplateData,
    options: EmailGenerationOptions = {}
  ): Promise<{ success: boolean; message: string }> {
    // For now, this is a mock implementation
    // In the future, this would integrate with an email service like SendGrid, AWS SES, etc.
    
    const emailPreview = this.generateEstimateEmailPreview(estimateId, data, options);
    
    console.log('Mock email sending:', {
      to: emailPreview.recipientEmail,
      subject: emailPreview.subject,
      estimateId,
    });

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      message: `Email would be sent to ${emailPreview.recipientEmail} (mock implementation)`,
    };
  }
}
