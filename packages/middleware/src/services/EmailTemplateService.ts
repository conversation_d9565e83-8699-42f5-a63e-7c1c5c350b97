import Handlebars from 'handlebars';

export interface EmailTemplateData {
  estimate: {
    id: number;
    name?: string;
    description?: string;
    createdAt: Date;
    updatedAt: Date;
  };
  customer: {
    customerDisplayName?: string;
    companyName?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    phoneNumber?: string;
    billingStreetAddress1?: string;
    billingStreetAddress2?: string;
    billingCity?: string;
    billingState?: string;
    billingZipCode?: string;
    billingCountry?: string;
  };
  lineItems: Array<{
    type: 'Labor' | 'Materials' | 'Equipment';
    item: string;
    units: string;
    time?: string;
    rate: number;
    price: number;
  }>;
  totalPrice: number;
}

export class EmailTemplateService {
  private static instance: EmailTemplateService;
  private templates: Map<string, HandlebarsTemplateDelegate> = new Map();

  private constructor() {
    this.initializeHelpers();
    this.loadTemplates();
  }

  public static getInstance(): EmailTemplateService {
    if (!EmailTemplateService.instance) {
      EmailTemplateService.instance = new EmailTemplateService();
    }
    return EmailTemplateService.instance;
  }

  private initializeHelpers(): void {
    // Helper for currency formatting
    Handlebars.registerHelper('currency', (amount: number) => {
      if (typeof amount !== 'number') {
        return '$0.00';
      }
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    });

    // Helper for date formatting
    Handlebars.registerHelper('formatDate', (date: Date) => {
      if (!date) return '';
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }).format(new Date(date));
    });

    // Helper for customer name
    Handlebars.registerHelper('customerName', (customer: any) => {
      if (customer.customerDisplayName) {
        return customer.customerDisplayName;
      }
      if (customer.companyName) {
        return customer.companyName;
      }
      const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
      return nameParts.length > 0 ? nameParts.join(' ') : 'Customer';
    });

    // Helper for address formatting
    Handlebars.registerHelper('formatAddress', (customer: any) => {
      const addressLines: string[] = [];
      
      if (customer.billingStreetAddress1) {
        addressLines.push(customer.billingStreetAddress1);
      }
      
      if (customer.billingStreetAddress2) {
        addressLines.push(customer.billingStreetAddress2);
      }
      
      const cityStateZip = [
        customer.billingCity,
        customer.billingState,
        customer.billingZipCode
      ].filter(Boolean).join(', ');
      
      if (cityStateZip) {
        addressLines.push(cityStateZip);
      }
      
      if (customer.billingCountry && customer.billingCountry !== 'US' && customer.billingCountry !== 'USA') {
        addressLines.push(customer.billingCountry);
      }
      
      return addressLines.join('<br>');
    });
  }

  private loadTemplates(): void {
    // Default estimate email template - this can be customized in the future
    const defaultEstimateTemplate = this.getDefaultEstimateEmailTemplate();
    const compiledTemplate = Handlebars.compile(defaultEstimateTemplate);
    this.templates.set('default-estimate-email', compiledTemplate);
  }

  public renderTemplate(templateName: string, data: EmailTemplateData): string {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Email template '${templateName}' not found`);
    }
    return template(data);
  }

  // Future enhancement: Allow custom templates to be loaded
  public addCustomTemplate(name: string, templateHtml: string): void {
    const compiledTemplate = Handlebars.compile(templateHtml);
    this.templates.set(name, compiledTemplate);
  }

  private getDefaultEstimateEmailTemplate(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estimate {{estimate.id}} - PROJECT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .email-header {
            background-color: #1976d2;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .company-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .email-title {
            font-size: 18px;
            opacity: 0.9;
        }
        .email-content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .estimate-summary {
            background-color: #f8f9fa;
            border-left: 4px solid #1976d2;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .estimate-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .detail-section {
            flex: 1;
            min-width: 250px;
            margin-bottom: 20px;
        }
        .section-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .line-items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }
        .line-items-table th {
            background-color: #1976d2;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }
        .line-items-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e0e0e0;
            font-size: 14px;
        }
        .line-items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .text-right {
            text-align: right;
        }
        .total-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }
        .total-label {
            font-weight: bold;
            font-size: 16px;
        }
        .total-value {
            font-weight: bold;
            font-size: 16px;
            color: #1976d2;
        }
        .grand-total {
            font-size: 20px;
            border-top: 2px solid #1976d2;
            padding-top: 15px;
            margin-top: 15px;
        }
        .email-footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #e0e0e0;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #1976d2;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 0 10px;
        }
        .contact-info {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 0;
            }
            .estimate-details {
                flex-direction: column;
            }
            .line-items-table {
                font-size: 12px;
            }
            .line-items-table th,
            .line-items-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <div class="company-name">PROJECT</div>
            <div class="email-title">Estimate #{{estimate.id}}</div>
        </div>

        <div class="email-content">
            <div class="greeting">
                Dear {{customerName customer}},
            </div>

            <p>Thank you for your interest in our services. Please find your estimate details below:</p>

            <div class="estimate-summary">
                <div class="section-title">Estimate Summary</div>
                {{#if estimate.name}}
                <div><strong>Project:</strong> {{estimate.name}}</div>
                {{/if}}
                {{#if estimate.description}}
                <div><strong>Description:</strong> {{estimate.description}}</div>
                {{/if}}
                <div><strong>Date:</strong> {{formatDate estimate.createdAt}}</div>
                <div><strong>Total Amount:</strong> {{currency totalPrice}}</div>
            </div>

            <div class="estimate-details">
                <div class="detail-section">
                    <div class="section-title">Bill To</div>
                    <div><strong>{{customerName customer}}</strong></div>
                    {{#if customer.email}}
                    <div>{{customer.email}}</div>
                    {{/if}}
                    {{#if customer.phoneNumber}}
                    <div>{{customer.phoneNumber}}</div>
                    {{/if}}
                    <div>{{{formatAddress customer}}}</div>
                </div>
            </div>

            <div class="section-title">Line Items</div>
            <table class="line-items-table">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Item</th>
                        <th>Units</th>
                        <th>Time</th>
                        <th>Rate</th>
                        <th class="text-right">Price</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each lineItems}}
                    <tr>
                        <td>{{this.type}}</td>
                        <td>{{this.item}}</td>
                        <td>{{this.units}}</td>
                        <td>{{#if this.time}}{{this.time}}{{else}}—{{/if}}</td>
                        <td>{{currency this.rate}}</td>
                        <td class="text-right">{{currency this.price}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>

            <div class="total-section">
                <div class="total-row grand-total">
                    <div class="total-label">TOTAL:</div>
                    <div class="total-value">{{currency totalPrice}}</div>
                </div>
            </div>

            <div class="cta-section">
                <p>If you have any questions about this estimate, please don't hesitate to contact us.</p>
                <a href="mailto:<EMAIL>" class="cta-button">Reply to this Email</a>
                <a href="tel:+1234567890" class="cta-button">Call Us</a>
            </div>

            <p>We look forward to working with you!</p>

            <p>Best regards,<br>
            <strong>PROJECT Team</strong></p>
        </div>

        <div class="email-footer">
            <div class="contact-info">
                <strong>PROJECT</strong><br>
                Email: <EMAIL> | Phone: (*************<br>
                Visit us at: www.project.com
            </div>
            <div style="margin-top: 15px; font-size: 12px; color: #999;">
                This estimate is valid for 30 days from the date issued.
            </div>
        </div>
    </div>
</body>
</html>
    `;
  }
}
