import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import path from 'path';
import fs from 'fs/promises';
import { PdfTemplateService, PdfTemplateData } from './PdfTemplateService';

export interface PdfGenerationOptions {
  templateName?: string;
  format?: 'A4' | 'Letter';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
}

export class PdfGenerationService {
  private static instance: PdfGenerationService;
  private browser: Browser | null = null;
  private templateService: PdfTemplateService;
  private pdfStoragePath: string;

  private constructor() {
    this.templateService = PdfTemplateService.getInstance();
    // Store PDFs in a pdfs directory within the middleware package
    this.pdfStoragePath = path.join(__dirname, '../../pdfs');
    this.ensurePdfDirectory();
  }

  public static getInstance(): PdfGenerationService {
    if (!PdfGenerationService.instance) {
      PdfGenerationService.instance = new PdfGenerationService();
    }
    return PdfGenerationService.instance;
  }

  private async ensurePdfDirectory(): Promise<void> {
    try {
      await fs.access(this.pdfStoragePath);
    } catch {
      await fs.mkdir(this.pdfStoragePath, { recursive: true });
    }
  }

  private async getBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });
    }
    return this.browser;
  }

  public async generateEstimatePdf(
    estimateId: number,
    data: PdfTemplateData,
    options: PdfGenerationOptions = {}
  ): Promise<string> {
    const browser = await this.getBrowser();
    const page: Page = await browser.newPage();

    try {
      // Set default options
      const templateName = options.templateName || 'default-estimate';
      const format = options.format || 'A4';
      const margin = options.margin || {
        top: '20mm',
        right: '20mm',
        bottom: '20mm',
        left: '20mm'
      };

      // Generate HTML from template
      const html = this.templateService.renderTemplate(templateName, data);

      // Set page content
      await page.setContent(html, {
        waitUntil: 'networkidle0'
      });

      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: format as any,
        margin,
        printBackground: true,
        preferCSSPageSize: false
      });

      // Save PDF to filesystem
      const filename = `estimate-${estimateId}-${Date.now()}.pdf`;
      const filePath = path.join(this.pdfStoragePath, filename);
      await fs.writeFile(filePath, pdfBuffer);

      return filename;
    } finally {
      await page.close();
    }
  }

  public async getPdfPath(filename: string): Promise<string> {
    const filePath = path.join(this.pdfStoragePath, filename);
    
    // Check if file exists
    try {
      await fs.access(filePath);
      return filePath;
    } catch {
      throw new Error(`PDF file not found: ${filename}`);
    }
  }

  public async deletePdf(filename: string): Promise<void> {
    const filePath = path.join(this.pdfStoragePath, filename);
    
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.warn(`Failed to delete PDF file: ${filename}`, error);
    }
  }

  public async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // Clean up old PDF files (older than 24 hours)
  public async cleanupOldPdfs(): Promise<void> {
    try {
      const files = await fs.readdir(this.pdfStoragePath);
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      for (const file of files) {
        if (file.endsWith('.pdf')) {
          const filePath = path.join(this.pdfStoragePath, file);
          const stats = await fs.stat(filePath);
          
          if (now - stats.mtime.getTime() > maxAge) {
            await this.deletePdf(file);
            console.log(`Cleaned up old PDF: ${file}`);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup old PDFs:', error);
    }
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  const service = PdfGenerationService.getInstance();
  await service.cleanup();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  const service = PdfGenerationService.getInstance();
  await service.cleanup();
  process.exit(0);
});
