import Handlebars from 'handlebars';

export interface PdfTemplateData {
  estimate: {
    id: number;
    name?: string;
    description?: string;
    createdAt: Date;
    updatedAt: Date;
  };
  customer: {
    customerDisplayName?: string;
    companyName?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    phoneNumber?: string;
    billingStreetAddress1?: string;
    billingStreetAddress2?: string;
    billingCity?: string;
    billingState?: string;
    billingZipCode?: string;
    billingCountry?: string;
  };
  lineItems: Array<{
    type: 'Labor' | 'Materials' | 'Equipment';
    item: string;
    units: string;
    time?: string;
    rate: number;
    // Note: cost and margin are excluded from PDF as per requirements
    price: number;
  }>;
  totalPrice: number;
  // Note: totalCost is excluded from PDF as per requirements
}

export class PdfTemplateService {
  private static instance: PdfTemplateService;
  private templates: Map<string, HandlebarsTemplateDelegate> = new Map();

  private constructor() {
    this.initializeHelpers();
    this.loadTemplates();
  }

  public static getInstance(): PdfTemplateService {
    if (!PdfTemplateService.instance) {
      PdfTemplateService.instance = new PdfTemplateService();
    }
    return PdfTemplateService.instance;
  }

  private initializeHelpers(): void {
    // Helper for formatting currency
    Handlebars.registerHelper('currency', (value: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(value);
    });

    // Helper for formatting dates
    Handlebars.registerHelper('formatDate', (date: Date) => {
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }).format(new Date(date));
    });

    // Helper for customer display name
    Handlebars.registerHelper('customerName', (customer: any) => {
      if (customer.customerDisplayName) {
        return customer.customerDisplayName;
      }
      if (customer.companyName) {
        return customer.companyName;
      }
      const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
      return nameParts.length > 0 ? nameParts.join(' ') : 'Customer';
    });

    // Helper for address formatting
    Handlebars.registerHelper('formatAddress', (customer: any) => {
      const addressLines: string[] = [];
      
      if (customer.billingStreetAddress1) {
        addressLines.push(customer.billingStreetAddress1);
      }
      
      if (customer.billingStreetAddress2) {
        addressLines.push(customer.billingStreetAddress2);
      }
      
      const cityStateZip = [
        customer.billingCity,
        customer.billingState,
        customer.billingZipCode
      ].filter(Boolean).join(', ');
      
      if (cityStateZip) {
        addressLines.push(cityStateZip);
      }
      
      if (customer.billingCountry && customer.billingCountry !== 'US' && customer.billingCountry !== 'USA') {
        addressLines.push(customer.billingCountry);
      }
      
      return addressLines.join('<br>');
    });
  }

  private loadTemplates(): void {
    // Default estimate template - this can be customized in the future
    const defaultEstimateTemplate = this.getDefaultEstimateTemplate();
    const compiledTemplate = Handlebars.compile(defaultEstimateTemplate);
    this.templates.set('default-estimate', compiledTemplate);
  }

  public renderTemplate(templateName: string, data: PdfTemplateData): string {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }
    return template(data);
  }

  // Future enhancement: Allow custom templates to be loaded
  public addCustomTemplate(name: string, templateHtml: string): void {
    const compiledTemplate = Handlebars.compile(templateHtml);
    this.templates.set(name, compiledTemplate);
  }

  private getDefaultEstimateTemplate(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estimate {{estimate.id}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }
        .estimate-title {
            font-size: 24px;
            color: #666;
            margin-bottom: 10px;
        }
        .estimate-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .customer-info, .estimate-details {
            flex: 1;
            padding: 0 10px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .line-items {
            margin: 30px 0;
        }
        .line-items table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .line-items th,
        .line-items td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .line-items th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
        }
        .line-items tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-right {
            text-align: right;
        }
        .total-section {
            margin-top: 30px;
            text-align: right;
        }
        .total-row {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }
        .total-label {
            font-weight: bold;
            margin-right: 20px;
            min-width: 100px;
        }
        .total-value {
            font-weight: bold;
            min-width: 100px;
            text-align: right;
        }
        .grand-total {
            font-size: 18px;
            color: #1976d2;
            border-top: 2px solid #1976d2;
            padding-top: 10px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">PROJECT</div>
        <div class="estimate-title">ESTIMATE</div>
        <div>Estimate #{{estimate.id}}</div>
    </div>

    <div class="estimate-info">
        <div class="customer-info">
            <div class="section-title">BILL TO</div>
            <div><strong>{{customerName customer}}</strong></div>
            {{#if customer.email}}
            <div>{{customer.email}}</div>
            {{/if}}
            {{#if customer.phoneNumber}}
            <div>{{customer.phoneNumber}}</div>
            {{/if}}
            <div>{{{formatAddress customer}}}</div>
        </div>
        
        <div class="estimate-details">
            <div class="section-title">ESTIMATE DETAILS</div>
            {{#if estimate.name}}
            <div><strong>Name:</strong> {{estimate.name}}</div>
            {{/if}}
            {{#if estimate.description}}
            <div><strong>Description:</strong> {{estimate.description}}</div>
            {{/if}}
            <div><strong>Date:</strong> {{formatDate estimate.createdAt}}</div>
        </div>
    </div>

    <div class="line-items">
        <div class="section-title">LINE ITEMS</div>
        <table>
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Item</th>
                    <th>Units</th>
                    <th>Time</th>
                    <th>Rate</th>
                    <th class="text-right">Price</th>
                </tr>
            </thead>
            <tbody>
                {{#each lineItems}}
                <tr>
                    <td>{{this.type}}</td>
                    <td>{{this.item}}</td>
                    <td>{{this.units}}</td>
                    <td>{{#if this.time}}{{this.time}}{{else}}—{{/if}}</td>
                    <td>{{currency this.rate}}</td>
                    <td class="text-right">{{currency this.price}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
    </div>

    <div class="total-section">
        <div class="total-row grand-total">
            <div class="total-label">TOTAL:</div>
            <div class="total-value">{{currency totalPrice}}</div>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>Generated on {{formatDate estimate.updatedAt}}</p>
    </div>
</body>
</html>
    `;
  }
}
