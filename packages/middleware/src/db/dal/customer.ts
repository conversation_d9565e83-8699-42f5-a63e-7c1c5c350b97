import { Op } from 'sequelize';
import Customer, { CustomerInput, CustomerOutput } from '../models/Customer';

export const getAll = async (search?: string): Promise<CustomerOutput[]> => {
  const whereClause = search
    ? {
        [Op.or]: [
          { customerDisplayName: { [Op.like]: `%${search}%` } },
          { companyName: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } },
          { firstName: { [Op.like]: `%${search}%` } },
          { lastName: { [Op.like]: `%${search}%` } },
        ],
      }
    : {};

  return Customer.findAll({
    where: whereClause,
    order: [['customerDisplayName', 'ASC']],
  }) as Promise<CustomerOutput[]>;
};

export const getById = async (id: number): Promise<CustomerOutput | null> => {
  return Customer.findByPk(id) as Promise<CustomerOutput | null>;
};

export const create = async (
  payload: CustomerInput,
): Promise<CustomerOutput> => {
  return Customer.create(payload) as Promise<CustomerOutput>;
};

export const update = async (
  id: number,
  payload: Partial<CustomerInput>,
): Promise<CustomerOutput> => {
  const customer = await Customer.findByPk(id);
  if (!customer) {
    throw new Error(`Customer with id ${id} not found`);
  }

  await customer.update(payload);
  await customer.reload();
  return customer as CustomerOutput;
};

export const deleteById = async (id: number): Promise<boolean> => {
  const customer = await Customer.findByPk(id);
  if (!customer) {
    throw new Error(`Customer with id ${id} not found`);
  }

  await customer.destroy();
  return true;
};
