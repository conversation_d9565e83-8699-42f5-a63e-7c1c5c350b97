import { LookupCategory, LookupItem } from '../models';
import {
  LookupCategoryInput,
  LookupCategoryOutput,
  LookupItemInput,
  LookupItemOutput,
} from '../models/Lookup';

// Lookup Categories
export const getAllCategories = async (): Promise<LookupCategoryOutput[]> => {
  return LookupCategory.findAll({
    include: [
      {
        model: LookupItem,
        as: 'items',
        where: { isActive: true },
        required: false,
        order: [['sortOrder', 'ASC']],
      },
    ],
    where: { isActive: true },
    order: [['sortOrder', 'ASC']],
  });
};

export const getCategoryById = async (id: number): Promise<LookupCategoryOutput | null> => {
  return LookupCategory.findByPk(id, {
    include: [
      {
        model: LookupItem,
        as: 'items',
        order: [['sortOrder', 'ASC']],
      },
    ],
  });
};

export const createCategory = async (categoryData: LookupCategoryInput): Promise<LookupCategoryOutput> => {
  return LookupCategory.create(categoryData);
};

export const updateCategory = async (
  id: number,
  categoryData: Partial<LookupCategoryInput>
): Promise<LookupCategoryOutput> => {
  const category = await LookupCategory.findByPk(id);
  if (!category) {
    throw new Error('Lookup category not found');
  }
  await category.update(categoryData);
  return getCategoryById(id) as Promise<LookupCategoryOutput>;
};

export const deleteCategoryById = async (id: number): Promise<boolean> => {
  const category = await LookupCategory.findByPk(id);
  if (!category) {
    throw new Error('Lookup category not found');
  }
  
  // Delete all items in this category first
  await LookupItem.destroy({ where: { categoryId: id } });
  
  // Delete the category
  await category.destroy();
  return true;
};

// Lookup Items
export const getAllItems = async (): Promise<LookupItemOutput[]> => {
  return LookupItem.findAll({
    include: [
      {
        model: LookupCategory,
        as: 'category',
      },
      {
        model: LookupItem,
        as: 'parent',
      },
      {
        model: LookupItem,
        as: 'children',
        where: { isActive: true },
        required: false,
        order: [['sortOrder', 'ASC']],
      },
    ],
    where: { isActive: true },
    order: [['sortOrder', 'ASC']],
  });
};

export const getItemById = async (id: number): Promise<LookupItemOutput | null> => {
  return LookupItem.findByPk(id, {
    include: [
      {
        model: LookupCategory,
        as: 'category',
      },
      {
        model: LookupItem,
        as: 'parent',
      },
      {
        model: LookupItem,
        as: 'children',
        order: [['sortOrder', 'ASC']],
      },
    ],
  });
};

export const getItemsByCategory = async (categoryId: number): Promise<LookupItemOutput[]> => {
  return LookupItem.findAll({
    where: { categoryId, isActive: true },
    include: [
      {
        model: LookupItem,
        as: 'parent',
      },
      {
        model: LookupItem,
        as: 'children',
        where: { isActive: true },
        required: false,
        order: [['sortOrder', 'ASC']],
      },
    ],
    order: [['sortOrder', 'ASC']],
  });
};

export const getItemsByParent = async (parentId: number): Promise<LookupItemOutput[]> => {
  return LookupItem.findAll({
    where: { parentId, isActive: true },
    include: [
      {
        model: LookupCategory,
        as: 'category',
      },
    ],
    order: [['sortOrder', 'ASC']],
  });
};

export const createItem = async (itemData: LookupItemInput): Promise<LookupItemOutput> => {
  return LookupItem.create(itemData);
};

export const updateItem = async (
  id: number,
  itemData: Partial<LookupItemInput>
): Promise<LookupItemOutput> => {
  const item = await LookupItem.findByPk(id);
  if (!item) {
    throw new Error('Lookup item not found');
  }
  await item.update(itemData);
  return getItemById(id) as Promise<LookupItemOutput>;
};

export const deleteItemById = async (id: number): Promise<boolean> => {
  const item = await LookupItem.findByPk(id);
  if (!item) {
    throw new Error('Lookup item not found');
  }
  
  // Delete all children items first
  await LookupItem.destroy({ where: { parentId: id } });
  
  // Delete the item
  await item.destroy();
  return true;
};

export const updateItemsOrder = async (items: { id: number; sortOrder: number }[]): Promise<void> => {
  const updatePromises = items.map(({ id, sortOrder }) =>
    LookupItem.update({ sortOrder }, { where: { id } })
  );
  await Promise.all(updatePromises);
};

export const updateCategoriesOrder = async (categories: { id: number; sortOrder: number }[]): Promise<void> => {
  const updatePromises = categories.map(({ id, sortOrder }) =>
    LookupCategory.update({ sortOrder }, { where: { id } })
  );
  await Promise.all(updatePromises);
};
