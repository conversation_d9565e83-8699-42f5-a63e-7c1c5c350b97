-- Create lookup_categories table
CREATE TABLE IF NOT EXISTS lookup_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT 1,
  sort_order INTEGER NOT NULL DEFAULT 0,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create lookup_items table
CREATE TABLE IF NOT EXISTS lookup_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  category_id INTEGER NOT NULL,
  parent_id INTEGER,
  name TEXT NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT 1,
  sort_order INTEGER NOT NULL DEFAULT 0,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (category_id) REFERENCES lookup_categories(id) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES lookup_items(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_lookup_categories_name ON lookup_categories(name);
CREATE INDEX IF NOT EXISTS idx_lookup_categories_active ON lookup_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_lookup_categories_sort ON lookup_categories(sort_order);

CREATE INDEX IF NOT EXISTS idx_lookup_items_category ON lookup_items(category_id);
CREATE INDEX IF NOT EXISTS idx_lookup_items_parent ON lookup_items(parent_id);
CREATE INDEX IF NOT EXISTS idx_lookup_items_active ON lookup_items(is_active);
CREATE INDEX IF NOT EXISTS idx_lookup_items_sort ON lookup_items(sort_order);
CREATE INDEX IF NOT EXISTS idx_lookup_items_value ON lookup_items(value);

-- Insert default lookup categories
INSERT OR IGNORE INTO lookup_categories (name, description, sort_order) VALUES
('Line Item Types', 'Main categories for estimate line items', 1),
('Labor Items', 'Specific labor items available for estimates', 2),
('Material Items', 'Specific material items available for estimates', 3),
('Equipment Items', 'Specific equipment items available for estimates', 4);

-- Insert default line item types
INSERT OR IGNORE INTO lookup_items (category_id, name, value, sort_order) VALUES
((SELECT id FROM lookup_categories WHERE name = 'Line Item Types'), 'Labor', 'Labor', 1),
((SELECT id FROM lookup_categories WHERE name = 'Line Item Types'), 'Materials', 'Materials', 2),
((SELECT id FROM lookup_categories WHERE name = 'Line Item Types'), 'Equipment', 'Equipment', 3);

-- Insert default labor items (dependent on Labor type)
INSERT OR IGNORE INTO lookup_items (category_id, parent_id, name, value, sort_order) VALUES
((SELECT id FROM lookup_categories WHERE name = 'Labor Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Labor' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Digout', 'Digout', 1),
((SELECT id FROM lookup_categories WHERE name = 'Labor Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Labor' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Paving', 'Paving', 2);

-- Insert default material items (dependent on Materials type)
INSERT OR IGNORE INTO lookup_items (category_id, parent_id, name, value, sort_order) VALUES
((SELECT id FROM lookup_categories WHERE name = 'Material Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Materials' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Asphalt', 'Asphalt', 1),
((SELECT id FROM lookup_categories WHERE name = 'Material Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Materials' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Concrete', 'Concrete', 2),
((SELECT id FROM lookup_categories WHERE name = 'Material Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Materials' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Gravel', 'Gravel', 3),
((SELECT id FROM lookup_categories WHERE name = 'Material Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Materials' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Sealcoating', 'Sealcoating', 4);

-- Insert default equipment items (dependent on Equipment type)
INSERT OR IGNORE INTO lookup_items (category_id, parent_id, name, value, sort_order) VALUES
((SELECT id FROM lookup_categories WHERE name = 'Equipment Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Equipment' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Bobcat', 'Bobcat', 1),
((SELECT id FROM lookup_categories WHERE name = 'Equipment Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Equipment' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Trucks', 'Trucks', 2),
((SELECT id FROM lookup_categories WHERE name = 'Equipment Items'), 
 (SELECT id FROM lookup_items WHERE value = 'Equipment' AND category_id = (SELECT id FROM lookup_categories WHERE name = 'Line Item Types')), 
 'Paver', 'Paver', 3);
