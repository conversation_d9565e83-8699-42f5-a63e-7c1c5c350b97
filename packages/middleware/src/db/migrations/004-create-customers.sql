-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  -- Name and contact fields
  title TEXT,
  first_name TEXT,
  middle_name TEXT,
  last_name TEXT,
  suffix TEXT,
  company_name TEXT,
  customer_display_name TEXT,
  email TEXT,
  phone_number TEXT,
  mobile_number TEXT,
  fax TEXT,
  other TEXT,
  website TEXT,
  name_to_print_on_checks TEXT,
  
  -- Billing address fields
  billing_street_address_1 TEXT,
  billing_street_address_2 TEXT,
  billing_city TEXT,
  billing_state TEXT,
  billing_zip_code TEXT,
  billing_country TEXT,
  
  -- Shipping address fields
  shipping_street_address_1 TEXT,
  shipping_street_address_2 TEXT,
  shipping_city TEXT,
  shipping_state TEXT,
  shipping_zip_code TEXT,
  shipping_country TEXT,
  same_as_billing_address BOOLEAN NOT NULL DEFAULT 0,
  
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index on customer name for search
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_display_name);
CREATE INDEX IF NOT EXISTS idx_customers_company ON customers(company_name);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);

-- Add customer_id column to estimates table if it doesn't exist
ALTER TABLE estimates ADD COLUMN customer_id INTEGER REFERENCES customers(id);

-- Create index on customer_id in estimates table
CREATE INDEX IF NOT EXISTS idx_estimates_customer_id ON estimates(customer_id);
