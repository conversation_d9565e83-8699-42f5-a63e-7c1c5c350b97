import { DataTypes, Model, Optional } from 'sequelize';
import sequelizeConnection from '../config';
import Customer from './Customer';

interface EstimateAttributes {
  id: number;
  name?: string;
  description?: string;
  customerId?: number;
  totalCost: number;
  totalPrice: number;
  createdAt: Date;
  updatedAt: Date;
}

interface LineItemAttributes {
  id: number;
  estimateId: number;
  type: 'Labor' | 'Materials' | 'Equipment';
  item: string;
  units: string;
  time?: string;
  rate: number;
  margin: number;
  cost: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface EstimateInput
  extends Optional<EstimateAttributes, 'id' | 'createdAt' | 'updatedAt'> {}
export interface EstimateOutput extends EstimateAttributes {}

export interface LineItemInput
  extends Optional<LineItemAttributes, 'id' | 'createdAt' | 'updatedAt'> {}
export interface LineItemOutput extends LineItemAttributes {}

class Estimate
  extends Model<EstimateAttributes, EstimateInput>
  implements EstimateAttributes
{
  id!: number;
  name?: string;
  description?: string;
  customerId?: number;
  totalCost!: number;
  totalPrice!: number;
  createdAt!: Date;
  updatedAt!: Date;

  // Association
  lineItems?: LineItemOutput[];
}

class LineItem
  extends Model<LineItemAttributes, LineItemInput>
  implements LineItemAttributes
{
  id!: number;
  estimateId!: number;
  type!: 'Labor' | 'Materials' | 'Equipment';
  item!: string;
  units!: string;
  time?: string;
  rate!: number;
  margin!: number;
  cost!: number;
  price!: number;
  createdAt!: Date;
  updatedAt!: Date;
}

Estimate.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'customer_id',
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    totalCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'total_cost',
    },
    totalPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'total_price',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
    },
  },
  {
    sequelize: sequelizeConnection,
    timestamps: true,
    tableName: 'estimates',
  },
);

LineItem.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    estimateId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'estimate_id',
      references: {
        model: Estimate,
        key: 'id',
      },
    },
    type: {
      type: DataTypes.ENUM('Labor', 'Materials', 'Equipment'),
      allowNull: false,
    },
    item: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    units: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    time: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    rate: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    margin: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
    },
    cost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
    },
  },
  {
    sequelize: sequelizeConnection,
    timestamps: true,
    tableName: 'line_items',
  },
);

// Define associations
Estimate.hasMany(LineItem, {
  foreignKey: 'estimateId',
  as: 'lineItems',
});

LineItem.belongsTo(Estimate, {
  foreignKey: 'estimateId',
  as: 'estimate',
});

Estimate.belongsTo(Customer, {
  foreignKey: 'customerId',
  as: 'customer',
});

Customer.hasMany(Estimate, {
  foreignKey: 'customerId',
  as: 'estimates',
});

export { Estimate, LineItem };
export default Estimate;
