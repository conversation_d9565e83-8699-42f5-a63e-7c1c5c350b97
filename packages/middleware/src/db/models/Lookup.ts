import { DataTypes, Model, Optional } from 'sequelize';
import sequelizeConnection from '../config';

// Lookup Category (e.g., "Line Item Types", "Labor Items", "Material Items")
interface LookupCategoryAttributes {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// Lookup Item (e.g., "Labor", "Digout", "Asphalt")
interface LookupItemAttributes {
  id: number;
  categoryId: number;
  parentId?: number; // For dependent lookups
  name: string;
  value: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface LookupCategoryInput
  extends Optional<LookupCategoryAttributes, 'id' | 'createdAt' | 'updatedAt'> {}
export interface LookupCategoryOutput extends LookupCategoryAttributes {
  items?: LookupItemOutput[];
}

export interface LookupItemInput
  extends Optional<LookupItemAttributes, 'id' | 'createdAt' | 'updatedAt'> {}
export interface LookupItemOutput extends LookupItemAttributes {
  category?: LookupCategoryOutput;
  parent?: LookupItemOutput;
  children?: LookupItemOutput[];
}

class LookupCategory
  extends Model<LookupCategoryAttributes, LookupCategoryInput>
  implements LookupCategoryAttributes
{
  id!: number;
  name!: string;
  description?: string;
  isActive!: boolean;
  sortOrder!: number;
  createdAt!: Date;
  updatedAt!: Date;

  // Association
  items?: LookupItemOutput[];
}

class LookupItem
  extends Model<LookupItemAttributes, LookupItemInput>
  implements LookupItemAttributes
{
  id!: number;
  categoryId!: number;
  parentId?: number;
  name!: string;
  value!: string;
  description?: string;
  isActive!: boolean;
  sortOrder!: number;
  createdAt!: Date;
  updatedAt!: Date;

  // Associations
  category?: LookupCategoryOutput;
  parent?: LookupItemOutput;
  children?: LookupItemOutput[];
}

LookupCategory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active',
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'sort_order',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
    },
  },
  {
    sequelize: sequelizeConnection,
    timestamps: true,
    tableName: 'lookup_categories',
  },
);

LookupItem.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'category_id',
      references: {
        model: LookupCategory,
        key: 'id',
      },
    },
    parentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'parent_id',
      references: {
        model: 'lookup_items',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    value: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active',
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'sort_order',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
    },
  },
  {
    sequelize: sequelizeConnection,
    timestamps: true,
    tableName: 'lookup_items',
  },
);

// Define associations
LookupCategory.hasMany(LookupItem, {
  foreignKey: 'categoryId',
  as: 'items',
});

LookupItem.belongsTo(LookupCategory, {
  foreignKey: 'categoryId',
  as: 'category',
});

// Self-referencing association for dependent lookups
LookupItem.hasMany(LookupItem, {
  foreignKey: 'parentId',
  as: 'children',
});

LookupItem.belongsTo(LookupItem, {
  foreignKey: 'parentId',
  as: 'parent',
});

export { LookupCategory, LookupItem };
export default { LookupCategory, LookupItem };
