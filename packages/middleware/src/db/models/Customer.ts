import { DataTypes, Model } from 'sequelize';
import sequelizeConnection from '../config';

interface CustomerAttributes {
  id: number;
  // Name and contact fields
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  companyName?: string;
  customerDisplayName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  other?: string;
  website?: string;
  nameToPrintOnChecks?: string;

  // Billing address fields
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;

  // Shipping address fields
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress: boolean;

  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerInput
  extends Omit<CustomerAttributes, 'id' | 'createdAt' | 'updatedAt'> {}
export interface CustomerOutput extends CustomerAttributes {}

class Customer
  extends Model<CustomerAttributes, CustomerInput>
  implements CustomerAttributes
{
  id!: number;

  // Name and contact fields
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  companyName?: string;
  customerDisplayName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  other?: string;
  website?: string;
  nameToPrintOnChecks?: string;

  // Billing address fields
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;

  // Shipping address fields
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress!: boolean;

  createdAt!: Date;
  updatedAt!: Date;
}

Customer.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    // Name and contact fields
    title: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'title',
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'first_name',
    },
    middleName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'middle_name',
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'last_name',
    },
    suffix: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'suffix',
    },
    companyName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'company_name',
    },
    customerDisplayName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'customer_display_name',
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'email',
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'phone_number',
    },
    mobileNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'mobile_number',
    },
    fax: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'fax',
    },
    other: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'other',
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'website',
    },
    nameToPrintOnChecks: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_to_print_on_checks',
    },

    // Billing address fields
    billingStreetAddress1: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_street_address_1',
    },
    billingStreetAddress2: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_street_address_2',
    },
    billingCity: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_city',
    },
    billingState: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_state',
    },
    billingZipCode: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_zip_code',
    },
    billingCountry: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_country',
    },

    // Shipping address fields
    shippingStreetAddress1: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_street_address_1',
    },
    shippingStreetAddress2: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_street_address_2',
    },
    shippingCity: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_city',
    },
    shippingState: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_state',
    },
    shippingZipCode: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_zip_code',
    },
    shippingCountry: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_country',
    },
    sameAsBillingAddress: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'same_as_billing_address',
    },

    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'updated_at',
    },
  },
  {
    sequelize: sequelizeConnection,
    paranoid: false,
    timestamps: true,
    tableName: 'customers',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export default Customer;
