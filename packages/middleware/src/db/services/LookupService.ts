import * as lookupDal from '../dal/lookup';
import {
  LookupCategoryInput,
  LookupCategoryOutput,
  LookupItemInput,
  LookupItemOutput,
} from '../models/Lookup';

// Lookup Categories
export const getAllCategories = (): Promise<LookupCategoryOutput[]> => {
  return lookupDal.getAllCategories();
};

export const getCategoryById = (id: number): Promise<LookupCategoryOutput | null> => {
  return lookupDal.getCategoryById(id);
};

export const createCategory = async (payload: LookupCategoryInput): Promise<LookupCategoryOutput> => {
  // Basic validation
  if (!payload.name || payload.name.trim() === '') {
    throw new Error('Category name is required');
  }

  // Set default sort order if not provided
  if (payload.sortOrder === undefined) {
    const categories = await lookupDal.getAllCategories();
    payload.sortOrder = categories.length;
  }

  return lookupDal.createCategory(payload);
};

export const updateCategory = async (
  id: number,
  payload: Partial<LookupCategoryInput>
): Promise<LookupCategoryOutput> => {
  // Basic validation
  if (payload.name !== undefined && (!payload.name || payload.name.trim() === '')) {
    throw new Error('Category name is required');
  }

  return lookupDal.updateCategory(id, payload);
};

export const deleteCategoryById = async (id: number): Promise<boolean> => {
  return lookupDal.deleteCategoryById(id);
};

export const updateCategoriesOrder = async (categories: { id: number; sortOrder: number }[]): Promise<void> => {
  return lookupDal.updateCategoriesOrder(categories);
};

// Lookup Items
export const getAllItems = (): Promise<LookupItemOutput[]> => {
  return lookupDal.getAllItems();
};

export const getItemById = (id: number): Promise<LookupItemOutput | null> => {
  return lookupDal.getItemById(id);
};

export const getItemsByCategory = (categoryId: number): Promise<LookupItemOutput[]> => {
  return lookupDal.getItemsByCategory(categoryId);
};

export const getItemsByParent = (parentId: number): Promise<LookupItemOutput[]> => {
  return lookupDal.getItemsByParent(parentId);
};

export const createItem = async (payload: LookupItemInput): Promise<LookupItemOutput> => {
  // Basic validation
  if (!payload.name || payload.name.trim() === '') {
    throw new Error('Item name is required');
  }

  if (!payload.value || payload.value.trim() === '') {
    throw new Error('Item value is required');
  }

  if (!payload.categoryId) {
    throw new Error('Category ID is required');
  }

  // Set default sort order if not provided
  if (payload.sortOrder === undefined) {
    const items = await lookupDal.getItemsByCategory(payload.categoryId);
    payload.sortOrder = items.length;
  }

  return lookupDal.createItem(payload);
};

export const updateItem = async (
  id: number,
  payload: Partial<LookupItemInput>
): Promise<LookupItemOutput> => {
  // Basic validation
  if (payload.name !== undefined && (!payload.name || payload.name.trim() === '')) {
    throw new Error('Item name is required');
  }

  if (payload.value !== undefined && (!payload.value || payload.value.trim() === '')) {
    throw new Error('Item value is required');
  }

  return lookupDal.updateItem(id, payload);
};

export const deleteItemById = async (id: number): Promise<boolean> => {
  return lookupDal.deleteItemById(id);
};

export const updateItemsOrder = async (items: { id: number; sortOrder: number }[]): Promise<void> => {
  return lookupDal.updateItemsOrder(items);
};

// Helper functions for estimate form integration
export const getLineItemTypes = async (): Promise<LookupItemOutput[]> => {
  const categories = await lookupDal.getAllCategories();
  const lineItemTypesCategory = categories.find(cat => cat.name === 'Line Item Types');
  
  if (!lineItemTypesCategory) {
    return [];
  }

  return lookupDal.getItemsByCategory(lineItemTypesCategory.id);
};

export const getItemsForLineItemType = async (lineItemType: string): Promise<LookupItemOutput[]> => {
  // Find the parent item (e.g., "Labor", "Materials", "Equipment")
  const allItems = await lookupDal.getAllItems();
  const parentItem = allItems.find(item => item.value === lineItemType);
  
  if (!parentItem) {
    return [];
  }

  // Get all items that have this parent
  return lookupDal.getItemsByParent(parentItem.id);
};
