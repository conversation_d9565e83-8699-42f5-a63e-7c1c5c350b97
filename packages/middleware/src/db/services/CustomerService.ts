import * as customerDal from '../dal/customer';
import { CustomerInput, CustomerOutput } from '../models/Customer';

export const getAll = (search?: string): Promise<CustomerOutput[]> => {
  return customerDal.getAll(search);
};

export const getById = (id: number): Promise<CustomerOutput | null> => {
  return customerDal.getById(id);
};

export const create = async (payload: CustomerInput): Promise<CustomerOutput> => {
  // Basic validation
  if (!payload.customerDisplayName && !payload.companyName && !payload.firstName && !payload.lastName) {
    throw new Error('At least one of customer display name, company name, first name, or last name is required');
  }

  // Auto-generate customer display name if not provided
  if (!payload.customerDisplayName) {
    if (payload.companyName) {
      payload.customerDisplayName = payload.companyName;
    } else {
      const nameParts = [payload.firstName, payload.lastName].filter(Boolean);
      payload.customerDisplayName = nameParts.join(' ');
    }
  }

  // Validate email format if provided
  if (payload.email && !isValidEmail(payload.email)) {
    throw new Error('Invalid email format');
  }

  return customerDal.create(payload);
};

export const update = async (
  id: number,
  payload: Partial<CustomerInput>
): Promise<CustomerOutput> => {
  // Validate email format if provided
  if (payload.email && !isValidEmail(payload.email)) {
    throw new Error('Invalid email format');
  }

  return customerDal.update(id, payload);
};

export const deleteById = async (id: number): Promise<boolean> => {
  return customerDal.deleteById(id);
};

// Helper function to validate email
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
