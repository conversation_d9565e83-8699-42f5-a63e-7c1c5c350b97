import { Request, Response } from 'express';
import * as customerService from '../../../db/services/CustomerService';
import { CustomerInput } from '../../../db/models/Customer';

export const getAllCustomers = async (req: Request, res: Response) => {
  try {
    const search = req.query.search as string;
    const customers = await customerService.getAll(search);
    return res.status(200).json(customers);
  } catch (error) {
    console.error('Error fetching customers:', error);
    return res.status(500).json({
      error: 'Failed to fetch customers',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export const getCustomerById = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    const customer = await customerService.getById(id);
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    return res.status(200).json(customer);
  } catch (error) {
    console.error('Error fetching customer:', error);
    return res.status(500).json({
      error: 'Failed to fetch customer',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export const createCustomer = async (req: Request, res: Response) => {
  try {
    const customerData: CustomerInput = req.body;
    const customer = await customerService.create(customerData);
    return res.status(201).json(customer);
  } catch (error) {
    console.error('Error creating customer:', error);
    return res.status(400).json({
      error: 'Failed to create customer',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export const updateCustomer = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    const customerData: Partial<CustomerInput> = req.body;
    const customer = await customerService.update(id, customerData);
    return res.status(200).json(customer);
  } catch (error) {
    console.error('Error updating customer:', error);
    const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 400;
    return res.status(statusCode).json({
      error: 'Failed to update customer',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export const deleteCustomer = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    await customerService.deleteById(id);
    return res.status(204).send();
  } catch (error) {
    console.error('Error deleting customer:', error);
    const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
    return res.status(statusCode).json({
      error: 'Failed to delete customer',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
