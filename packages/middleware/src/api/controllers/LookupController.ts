import { Request, Response } from 'express';
import * as lookupService from '../../db/services/LookupService';

export class LookupController {
  // Categories
  public async getAllCategories(req: Request, res: Response): Promise<Response> {
    try {
      const categories = await lookupService.getAllCategories();
      return res.json(categories);
    } catch (error) {
      console.error('Error fetching lookup categories:', error);
      return res.status(500).json({
        error: 'Failed to fetch lookup categories',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async getCategoryById(req: Request, res: Response): Promise<Response> {
    try {
      const id = Number(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid category ID' });
      }

      const category = await lookupService.getCategoryById(id);
      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }

      return res.json(category);
    } catch (error) {
      console.error('Error fetching lookup category:', error);
      return res.status(500).json({
        error: 'Failed to fetch lookup category',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async createCategory(req: Request, res: Response): Promise<Response> {
    try {
      const category = await lookupService.createCategory(req.body);
      return res.status(201).json(category);
    } catch (error) {
      console.error('Error creating lookup category:', error);
      return res.status(400).json({
        error: 'Failed to create lookup category',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async updateCategory(req: Request, res: Response): Promise<Response> {
    try {
      const id = Number(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid category ID' });
      }

      const category = await lookupService.updateCategory(id, req.body);
      return res.json(category);
    } catch (error) {
      console.error('Error updating lookup category:', error);
      return res.status(400).json({
        error: 'Failed to update lookup category',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async deleteCategory(req: Request, res: Response): Promise<Response> {
    try {
      const id = Number(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid category ID' });
      }

      await lookupService.deleteCategoryById(id);
      return res.status(204).send();
    } catch (error) {
      console.error('Error deleting lookup category:', error);
      return res.status(400).json({
        error: 'Failed to delete lookup category',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async updateCategoriesOrder(req: Request, res: Response): Promise<Response> {
    try {
      const { categories } = req.body;
      if (!Array.isArray(categories)) {
        return res.status(400).json({ error: 'Categories array is required' });
      }

      await lookupService.updateCategoriesOrder(categories);
      return res.json({ success: true });
    } catch (error) {
      console.error('Error updating categories order:', error);
      return res.status(400).json({
        error: 'Failed to update categories order',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Items
  public async getAllItems(req: Request, res: Response): Promise<Response> {
    try {
      const items = await lookupService.getAllItems();
      return res.json(items);
    } catch (error) {
      console.error('Error fetching lookup items:', error);
      return res.status(500).json({
        error: 'Failed to fetch lookup items',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async getItemById(req: Request, res: Response): Promise<Response> {
    try {
      const id = Number(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid item ID' });
      }

      const item = await lookupService.getItemById(id);
      if (!item) {
        return res.status(404).json({ error: 'Item not found' });
      }

      return res.json(item);
    } catch (error) {
      console.error('Error fetching lookup item:', error);
      return res.status(500).json({
        error: 'Failed to fetch lookup item',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async getItemsByCategory(req: Request, res: Response): Promise<Response> {
    try {
      const categoryId = Number(req.params.categoryId);
      if (isNaN(categoryId)) {
        return res.status(400).json({ error: 'Invalid category ID' });
      }

      const items = await lookupService.getItemsByCategory(categoryId);
      return res.json(items);
    } catch (error) {
      console.error('Error fetching lookup items by category:', error);
      return res.status(500).json({
        error: 'Failed to fetch lookup items by category',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async getItemsByParent(req: Request, res: Response): Promise<Response> {
    try {
      const parentId = Number(req.params.parentId);
      if (isNaN(parentId)) {
        return res.status(400).json({ error: 'Invalid parent ID' });
      }

      const items = await lookupService.getItemsByParent(parentId);
      return res.json(items);
    } catch (error) {
      console.error('Error fetching lookup items by parent:', error);
      return res.status(500).json({
        error: 'Failed to fetch lookup items by parent',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async createItem(req: Request, res: Response): Promise<Response> {
    try {
      const item = await lookupService.createItem(req.body);
      return res.status(201).json(item);
    } catch (error) {
      console.error('Error creating lookup item:', error);
      return res.status(400).json({
        error: 'Failed to create lookup item',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async updateItem(req: Request, res: Response): Promise<Response> {
    try {
      const id = Number(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid item ID' });
      }

      const item = await lookupService.updateItem(id, req.body);
      return res.json(item);
    } catch (error) {
      console.error('Error updating lookup item:', error);
      return res.status(400).json({
        error: 'Failed to update lookup item',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async deleteItem(req: Request, res: Response): Promise<Response> {
    try {
      const id = Number(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid item ID' });
      }

      await lookupService.deleteItemById(id);
      return res.status(204).send();
    } catch (error) {
      console.error('Error deleting lookup item:', error);
      return res.status(400).json({
        error: 'Failed to delete lookup item',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async updateItemsOrder(req: Request, res: Response): Promise<Response> {
    try {
      const { items } = req.body;
      if (!Array.isArray(items)) {
        return res.status(400).json({ error: 'Items array is required' });
      }

      await lookupService.updateItemsOrder(items);
      return res.json({ success: true });
    } catch (error) {
      console.error('Error updating items order:', error);
      return res.status(400).json({
        error: 'Failed to update items order',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Helper endpoints for estimate form
  public async getLineItemTypes(req: Request, res: Response): Promise<Response> {
    try {
      const types = await lookupService.getLineItemTypes();
      return res.json(types);
    } catch (error) {
      console.error('Error fetching line item types:', error);
      return res.status(500).json({
        error: 'Failed to fetch line item types',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  public async getItemsForLineItemType(req: Request, res: Response): Promise<Response> {
    try {
      const { lineItemType } = req.params;
      if (!lineItemType) {
        return res.status(400).json({ error: 'Line item type is required' });
      }

      const items = await lookupService.getItemsForLineItemType(lineItemType);
      return res.json(items);
    } catch (error) {
      console.error('Error fetching items for line item type:', error);
      return res.status(500).json({
        error: 'Failed to fetch items for line item type',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
