import { Request, Response } from 'express';
import { PdfGenerationService } from '../../services/PdfGenerationService';
import { PdfTemplateData } from '../../services/PdfTemplateService';
import * as estimateDAL from '../../db/dal/estimate';
import { LineItemOutput } from '../../db/models/Estimate';
import path from 'path';

export class PdfController {
  private pdfService: PdfGenerationService;

  constructor() {
    this.pdfService = PdfGenerationService.getInstance();
  }

  public generateEstimatePdf = async (
    req: Request,
    res: Response,
  ): Promise<void> => {
    try {
      const estimateId = parseInt(req.params.id);

      if (isNaN(estimateId)) {
        res.status(400).json({ error: 'Invalid estimate ID' });
        return;
      }

      // Fetch estimate data with customer and line items
      const estimate = (await estimateDAL.getById(estimateId)) as any;

      if (!estimate) {
        res.status(404).json({ error: 'Estimate not found' });
        return;
      }

      // Transform data for PDF template (excluding cost and margin)
      const pdfData: PdfTemplateData = {
        estimate: {
          id: estimate.id,
          name: estimate.name,
          description: estimate.description,
          createdAt: estimate.createdAt,
          updatedAt: estimate.updatedAt,
        },
        customer: estimate.customer || {},
        lineItems: (estimate.lineItems || []).map((item: LineItemOutput) => ({
          type: item.type,
          item: item.item,
          units: item.units,
          time: item.time,
          rate: item.rate,
          // Exclude cost and margin as per requirements
          price: item.price,
        })),
        totalPrice: estimate.totalPrice,
        // Exclude totalCost as per requirements
      };

      // Generate PDF
      const filename = await this.pdfService.generateEstimatePdf(
        estimateId,
        pdfData,
        {
          templateName: 'default-estimate',
          format: 'A4',
        },
      );

      res.json({
        success: true,
        filename,
        downloadUrl: `/api/pdfs/download/${filename}`,
        viewUrl: `/api/pdfs/view/${filename}`,
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      res.status(500).json({
        error: 'Failed to generate PDF',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  public downloadPdf = async (req: Request, res: Response): Promise<void> => {
    try {
      const filename = req.params.filename;

      if (!filename || !filename.endsWith('.pdf')) {
        res.status(400).json({ error: 'Invalid filename' });
        return;
      }

      const filePath = await this.pdfService.getPdfPath(filename);

      // Set headers for download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${filename}"`,
      );
      res.setHeader('Cache-Control', 'no-cache');

      // Send file
      res.sendFile(filePath);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: 'PDF file not found' });
      } else {
        res.status(500).json({ error: 'Failed to download PDF' });
      }
    }
  };

  public viewPdf = async (req: Request, res: Response): Promise<void> => {
    try {
      const filename = req.params.filename;

      if (!filename || !filename.endsWith('.pdf')) {
        res.status(400).json({ error: 'Invalid filename' });
        return;
      }

      const filePath = await this.pdfService.getPdfPath(filename);

      // Set headers for inline viewing
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
      res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour

      // Send file
      res.sendFile(filePath);
    } catch (error) {
      console.error('Error viewing PDF:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: 'PDF file not found' });
      } else {
        res.status(500).json({ error: 'Failed to view PDF' });
      }
    }
  };

  public printPdf = async (req: Request, res: Response): Promise<void> => {
    try {
      const filename = req.params.filename;

      if (!filename || !filename.endsWith('.pdf')) {
        res.status(400).json({ error: 'Invalid filename' });
        return;
      }

      const filePath = await this.pdfService.getPdfPath(filename);

      // Set headers for printing (opens in new tab)
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('X-Frame-Options', 'SAMEORIGIN'); // Allow iframe embedding

      // Send file
      res.sendFile(filePath);
    } catch (error) {
      console.error('Error printing PDF:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: 'PDF file not found' });
      } else {
        res.status(500).json({ error: 'Failed to print PDF' });
      }
    }
  };

  public cleanupOldPdfs = async (
    req: Request,
    res: Response,
  ): Promise<void> => {
    try {
      await this.pdfService.cleanupOldPdfs();
      res.json({ success: true, message: 'Old PDFs cleaned up successfully' });
    } catch (error) {
      console.error('Error cleaning up PDFs:', error);
      res.status(500).json({ error: 'Failed to cleanup old PDFs' });
    }
  };
}
