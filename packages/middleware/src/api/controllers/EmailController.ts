import { Request, Response } from 'express';
import { EmailGenerationService } from '../../services/EmailGenerationService';
import { EmailTemplateData } from '../../services/EmailTemplateService';
import * as estimateController from './estimate';
import { LineItemOutput } from '../../db/models/Estimate';

export class EmailController {
  private emailService: EmailGenerationService;

  constructor() {
    this.emailService = EmailGenerationService.getInstance();
  }

  // GET /api/v1/emails/estimates/:id/preview - Generate email preview
  public async generateEstimateEmailPreview(req: Request, res: Response): Promise<Response> {
    try {
      const estimateId = Number(req.params.id);
      if (isNaN(estimateId)) {
        return res.status(400).json({ error: 'Invalid estimate ID' });
      }

      // Get estimate data
      const estimate = await estimateController.getById(estimateId);
      if (!estimate) {
        return res.status(404).json({ error: 'Estimate not found' });
      }

      // Transform data for email template (excluding cost and margin)
      const emailData: EmailTemplateData = {
        estimate: {
          id: estimate.id,
          name: estimate.name,
          description: estimate.description,
          createdAt: estimate.createdAt,
          updatedAt: estimate.updatedAt,
        },
        customer: estimate.customer || {},
        lineItems: (estimate.lineItems || []).map((item: LineItemOutput) => ({
          type: item.type,
          item: item.item,
          units: item.units,
          time: item.time,
          rate: item.rate,
          // Exclude cost and margin as per requirements
          price: item.price,
        })),
        totalPrice: estimate.totalPrice,
        // Exclude totalCost as per requirements
      };

      // Generate email preview
      const emailPreview = this.emailService.generateEstimateEmailPreview(
        estimateId,
        emailData,
        {
          templateName: 'default-estimate-email',
          subject: req.query.subject as string,
          recipientEmail: req.query.recipientEmail as string,
          recipientName: req.query.recipientName as string,
        }
      );

      return res.json({
        success: true,
        estimateId,
        preview: emailPreview,
      });
    } catch (error) {
      console.error('Error generating email preview:', error);
      return res.status(500).json({
        error: 'Failed to generate email preview',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // POST /api/v1/emails/estimates/:id/send - Send estimate email (mock)
  public async sendEstimateEmail(req: Request, res: Response): Promise<Response> {
    try {
      const estimateId = Number(req.params.id);
      if (isNaN(estimateId)) {
        return res.status(400).json({ error: 'Invalid estimate ID' });
      }

      const { recipientEmail, recipientName, subject, customMessage } = req.body;

      // Get estimate data
      const estimate = await estimateController.getById(estimateId);
      if (!estimate) {
        return res.status(404).json({ error: 'Estimate not found' });
      }

      // Transform data for email template
      const emailData: EmailTemplateData = {
        estimate: {
          id: estimate.id,
          name: estimate.name,
          description: estimate.description,
          createdAt: estimate.createdAt,
          updatedAt: estimate.updatedAt,
        },
        customer: estimate.customer || {},
        lineItems: (estimate.lineItems || []).map((item: LineItemOutput) => ({
          type: item.type,
          item: item.item,
          units: item.units,
          time: item.time,
          rate: item.rate,
          price: item.price,
        })),
        totalPrice: estimate.totalPrice,
      };

      // Send email (mock implementation)
      const result = await this.emailService.sendEstimateEmail(
        estimateId,
        emailData,
        {
          templateName: 'default-estimate-email',
          subject,
          recipientEmail,
          recipientName,
        }
      );

      return res.json({
        success: result.success,
        message: result.message,
        estimateId,
        sentTo: recipientEmail,
      });
    } catch (error) {
      console.error('Error sending email:', error);
      return res.status(500).json({
        error: 'Failed to send email',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
