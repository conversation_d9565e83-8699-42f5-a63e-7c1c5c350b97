export interface Customer {
  id: number;
  // Name and contact fields
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  companyName?: string;
  customerDisplayName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  other?: string;
  website?: string;
  nameToPrintOnChecks?: string;
  
  // Billing address fields
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;
  
  // Shipping address fields
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress: boolean;
  
  createdAt: Date;
  updatedAt: Date;
}
