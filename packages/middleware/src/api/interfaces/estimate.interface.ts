export interface LineItem {
  id: number;
  estimateId: number;
  type: 'Labor' | 'Materials' | 'Equipment';
  item: string;
  units: string;
  time?: string;
  rate: number;
  margin: number;
  cost: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Estimate {
  id: number;
  name?: string;
  description?: string;
  customerId?: number;
  totalCost: number;
  totalPrice: number;
  createdAt: Date;
  updatedAt: Date;
  lineItems?: LineItem[];
  // Optional customer info for display (populated by DAL when included)
  customer?: {
    id: number;
    customerDisplayName?: string;
    companyName?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    phoneNumber?: string;
    billingStreetAddress1?: string;
    billingStreetAddress2?: string;
    billingCity?: string;
    billingState?: string;
    billingZipCode?: string;
    billingCountry?: string;
  };
}
