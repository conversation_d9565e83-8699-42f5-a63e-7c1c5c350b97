export interface LineItem {
  id: number;
  estimateId: number;
  type: 'Labor' | 'Materials' | 'Equipment';
  item: string;
  units: string;
  time?: string;
  rate: number;
  margin: number;
  cost: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Estimate {
  id: number;
  name?: string;
  description?: string;
  customerId?: number;
  totalCost: number;
  totalPrice: number;
  createdAt: Date;
  updatedAt: Date;
  lineItems?: LineItem[];
}
