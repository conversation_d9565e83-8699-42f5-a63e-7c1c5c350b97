import { Router } from 'express';
import { PdfController } from '../controllers/PdfController';

const router = Router();
const pdfController = new PdfController();

// Generate PDF for estimate
router.post('/estimates/:id/generate', pdfController.generateEstimatePdf);

// Download PDF file
router.get('/download/:filename', pdfController.downloadPdf);

// View PDF file (for iframe)
router.get('/view/:filename', pdfController.viewPdf);

// Print PDF file (opens in new tab)
router.get('/print/:filename', pdfController.printPdf);

// Cleanup old PDFs (admin endpoint)
router.post('/cleanup', pdfController.cleanupOldPdfs);

export default router;
