import { Router } from 'express';
import { EmailController } from '../controllers/EmailController';

const emailsRouter = Router();
const emailController = new EmailController();

// GET /api/v1/emails/estimates/:id/preview - Generate email preview
emailsRouter.get('/estimates/:id/preview', (req, res) => {
  emailController.generateEstimateEmailPreview(req, res);
});

// POST /api/v1/emails/estimates/:id/send - Send estimate email (mock)
emailsRouter.post('/estimates/:id/send', (req, res) => {
  emailController.sendEstimateEmail(req, res);
});

export default emailsRouter;
