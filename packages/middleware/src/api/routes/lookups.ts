import { Router } from 'express';
import { LookupController } from '../controllers/LookupController';

const lookupsRouter = Router();
const lookupController = new LookupController();

// Categories routes
lookupsRouter.get('/categories', (req, res) => {
  lookupController.getAllCategories(req, res);
});

lookupsRouter.get('/categories/:id', (req, res) => {
  lookupController.getCategoryById(req, res);
});

lookupsRouter.post('/categories', (req, res) => {
  lookupController.createCategory(req, res);
});

lookupsRouter.put('/categories/:id', (req, res) => {
  lookupController.updateCategory(req, res);
});

lookupsRouter.delete('/categories/:id', (req, res) => {
  lookupController.deleteCategory(req, res);
});

lookupsRouter.put('/categories/order', (req, res) => {
  lookupController.updateCategoriesOrder(req, res);
});

// Items routes
lookupsRouter.get('/items', (req, res) => {
  lookupController.getAllItems(req, res);
});

lookupsRouter.get('/items/:id', (req, res) => {
  lookupController.getItemById(req, res);
});

lookupsRouter.get('/categories/:categoryId/items', (req, res) => {
  lookupController.getItemsByCategory(req, res);
});

lookupsRouter.get('/items/:parentId/children', (req, res) => {
  lookupController.getItemsByParent(req, res);
});

lookupsRouter.post('/items', (req, res) => {
  lookupController.createItem(req, res);
});

lookupsRouter.put('/items/:id', (req, res) => {
  lookupController.updateItem(req, res);
});

lookupsRouter.delete('/items/:id', (req, res) => {
  lookupController.deleteItem(req, res);
});

lookupsRouter.put('/items/order', (req, res) => {
  lookupController.updateItemsOrder(req, res);
});

// Helper routes for estimate form
lookupsRouter.get('/line-item-types', (req, res) => {
  lookupController.getLineItemTypes(req, res);
});

lookupsRouter.get('/line-item-types/:lineItemType/items', (req, res) => {
  lookupController.getItemsForLineItemType(req, res);
});

export default lookupsRouter;
