import { Router } from 'express';

import estimatesRouter from './estimates';
import customersRouter from './customers';
import pdfRouter from './pdf';
import emailsRouter from './emails';
import lookupsRouter from './lookups';

const router = Router();

router.use('/estimates', estimatesRouter);
router.use('/customers', customersRouter);
router.use('/pdfs', pdfRouter);
router.use('/emails', emailsRouter);
router.use('/lookups', lookupsRouter);

export default router;
