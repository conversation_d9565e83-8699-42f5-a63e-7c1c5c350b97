{"name": "middleware", "version": "1.0.0", "main": "index.js", "license": "MIT", "private": true, "scripts": {"start": "nodemon src/index.ts", "build": "npx tsc"}, "dependencies": {"@types/handlebars": "^4.1.0", "@types/puppeteer": "^7.0.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "handlebars": "^4.7.8", "puppeteer": "^24.15.0", "sequelize": "^6.35.2", "sqlite3": "^5.1.6"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "eslint": "^8.56.0", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}